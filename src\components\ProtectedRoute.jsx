import React from 'react';
import { useAuth } from '../contexts/AuthContext';

/**
 * ProtectedRoute component that only renders children if user is authenticated
 * Uses the existing AuthContext for authentication state
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authenticated
 * @param {React.ComponentType} props.fallback - Component to render if not authenticated (optional)
 * @param {string} props.redirectTo - Path to redirect to if not authenticated (optional)
 * @param {boolean} props.requireEmailVerification - Whether to require email verification (optional)
 */
const ProtectedRoute = ({ 
  children, 
  fallback: Fallback,
  redirectTo,
  requireEmailVerification = false 
}) => {
  const { user, loading, isAuthenticated } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div style={styles.loadingContainer}>
        <div style={styles.loadingSpinner}>
          <div style={styles.spinner}></div>
          <p style={styles.loadingText}>Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!isAuthenticated || !user) {
    // If a custom fallback component is provided, render it
    if (Fallback) {
      return <Fallback />;
    }

    // If redirectTo is specified, redirect to that path
    if (redirectTo) {
      window.location.href = redirectTo;
      return (
        <div style={styles.redirectingContainer}>
          <p style={styles.redirectingText}>Redirecting to login...</p>
        </div>
      );
    }

    // Default: render login prompt
    return (
      <div style={styles.unauthorizedContainer}>
        <div style={styles.unauthorizedCard}>
          <h2 style={styles.unauthorizedTitle}>🔒 Access Restricted</h2>
          <p style={styles.unauthorizedMessage}>
            You need to be signed in to access this page.
          </p>
          <div style={styles.loginPrompt}>
            <p style={styles.promptText}>Please sign in to continue</p>
            <button 
              onClick={() => window.location.reload()}
              style={styles.refreshButton}
            >
              🔄 Refresh Page
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check email verification if required
  if (requireEmailVerification && !user.emailVerified) {
    return (
      <div style={styles.verificationContainer}>
        <div style={styles.verificationCard}>
          <h2 style={styles.verificationTitle}>📧 Email Verification Required</h2>
          <p style={styles.verificationMessage}>
            Please verify your email address to access this page.
          </p>
          <div style={styles.verificationInfo}>
            <p style={styles.emailText}>
              <strong>Email:</strong> {user.email}
            </p>
            <p style={styles.statusText}>
              <strong>Status:</strong> 
              <span style={styles.unverifiedStatus}>Not Verified</span>
            </p>
          </div>
          <div style={styles.verificationActions}>
            <button 
              onClick={() => window.location.reload()}
              style={styles.checkButton}
            >
              ✅ Check Verification Status
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User is authenticated (and email verified if required) - render children
  return <>{children}</>;
};

// Styles for the ProtectedRoute component
const styles = {
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f8fafc',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  loadingSpinner: {
    textAlign: 'center',
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '40px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e2e8f0'
  },
  spinner: {
    width: '40px',
    height: '40px',
    border: '4px solid #f1f5f9',
    borderTop: '4px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 16px'
  },
  loadingText: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0'
  },
  redirectingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f8fafc',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  redirectingText: {
    fontSize: '18px',
    color: '#64748b'
  },
  unauthorizedContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f8fafc',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  unauthorizedCard: {
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '40px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '400px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  unauthorizedTitle: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#dc2626',
    margin: '0 0 16px 0'
  },
  unauthorizedMessage: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 24px 0',
    lineHeight: '1.5'
  },
  loginPrompt: {
    padding: '20px',
    backgroundColor: '#fef2f2',
    borderRadius: '8px',
    border: '1px solid #fecaca'
  },
  promptText: {
    fontSize: '14px',
    color: '#7f1d1d',
    margin: '0 0 16px 0'
  },
  refreshButton: {
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#dc2626',
    color: 'white',
    border: 'none',
    borderRadius: '6px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  verificationContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f8fafc',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  verificationCard: {
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '40px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '450px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  verificationTitle: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#f59e0b',
    margin: '0 0 16px 0'
  },
  verificationMessage: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 24px 0',
    lineHeight: '1.5'
  },
  verificationInfo: {
    padding: '20px',
    backgroundColor: '#fffbeb',
    borderRadius: '8px',
    border: '1px solid #fed7aa',
    marginBottom: '24px',
    textAlign: 'left'
  },
  emailText: {
    fontSize: '14px',
    color: '#92400e',
    margin: '0 0 8px 0'
  },
  statusText: {
    fontSize: '14px',
    color: '#92400e',
    margin: '0'
  },
  unverifiedStatus: {
    color: '#dc2626',
    fontWeight: '600'
  },
  verificationActions: {
    display: 'flex',
    justifyContent: 'center'
  },
  checkButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#f59e0b',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  }
};

export default ProtectedRoute;
