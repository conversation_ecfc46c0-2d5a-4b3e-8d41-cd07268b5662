// Example usage of the firebase.js file
// This file demonstrates how to use the Firebase v9 modular SDK

import { 
  signInWithPopup, 
  signOut, 
  onAuthStateChanged,
  signInWithRedirect,
  getRedirectResult
} from 'firebase/auth';
import { auth, googleProvider } from '../firebase';

// Example 1: Sign in with Google using popup
export const signInWithGooglePopup = async () => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;
    console.log('User signed in:', user.displayName);
    return user;
  } catch (error) {
    console.error('Error during sign in:', error);
    throw error;
  }
};

// Example 2: Sign in with Google using redirect
export const signInWithGoogleRedirect = async () => {
  try {
    await signInWithRedirect(auth, googleProvider);
  } catch (error) {
    console.error('Error during redirect sign in:', error);
    throw error;
  }
};

// Example 3: Handle redirect result (call this on app initialization)
export const handleRedirectResult = async () => {
  try {
    const result = await getRedirectResult(auth);
    if (result) {
      const user = result.user;
      console.log('User signed in via redirect:', user.displayName);
      return user;
    }
  } catch (error) {
    console.error('Error handling redirect result:', error);
    throw error;
  }
};

// Example 4: Sign out
export const signOutUser = async () => {
  try {
    await signOut(auth);
    console.log('User signed out');
  } catch (error) {
    console.error('Error during sign out:', error);
    throw error;
  }
};

// Example 5: Listen to authentication state changes
export const setupAuthListener = (callback) => {
  const unsubscribe = onAuthStateChanged(auth, (user) => {
    if (user) {
      console.log('User is signed in:', user.displayName);
      callback({ user, isSignedIn: true });
    } else {
      console.log('User is signed out');
      callback({ user: null, isSignedIn: false });
    }
  });
  
  // Return unsubscribe function to clean up the listener
  return unsubscribe;
};

// Example 6: Get current user
export const getCurrentUser = () => {
  return auth.currentUser;
};

// Example 7: Check if user is signed in
export const isUserSignedIn = () => {
  return !!auth.currentUser;
};
