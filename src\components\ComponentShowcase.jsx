import React from 'react';
import GoogleSignIn from './GoogleSignIn';

const ComponentShowcase = () => {
  return (
    <div style={styles.container}>
      {/* Header */}
      <div style={styles.header}>
        <h1 style={styles.title}>
          🔐 GoogleSignIn Component
        </h1>
        <p style={styles.subtitle}>
          A complete, self-contained React functional component for Firebase Google Authentication
        </p>
      </div>

      {/* Live Demo */}
      <div style={styles.demoSection}>
        <h2 style={styles.sectionTitle}>Live Demo</h2>
        <div style={styles.demoContainer}>
          <GoogleSignIn />
        </div>
      </div>

      {/* Features Grid */}
      <div style={styles.featuresSection}>
        <h2 style={styles.sectionTitle}>Key Features</h2>
        <div style={styles.featuresGrid}>
          <div style={styles.featureCard}>
            <div style={styles.featureIcon}>🚀</div>
            <h3 style={styles.featureTitle}>Easy Integration</h3>
            <p style={styles.featureDescription}>
              Drop-in component with zero configuration required. 
              Just import and use!
            </p>
          </div>

          <div style={styles.featureCard}>
            <div style={styles.featureIcon}>🔄</div>
            <h3 style={styles.featureTitle}>Real-time State</h3>
            <p style={styles.featureDescription}>
              Automatically syncs with Firebase auth state changes. 
              No manual state management needed.
            </p>
          </div>

          <div style={styles.featureCard}>
            <div style={styles.featureIcon}>👤</div>
            <h3 style={styles.featureTitle}>User Profile</h3>
            <p style={styles.featureDescription}>
              Displays user name, email, profile picture, and 
              additional account information.
            </p>
          </div>

          <div style={styles.featureCard}>
            <div style={styles.featureIcon}>🛡️</div>
            <h3 style={styles.featureTitle}>Error Handling</h3>
            <p style={styles.featureDescription}>
              Comprehensive error handling for network issues, 
              popup blockers, and auth failures.
            </p>
          </div>

          <div style={styles.featureCard}>
            <div style={styles.featureIcon}>📱</div>
            <h3 style={styles.featureTitle}>Responsive Design</h3>
            <p style={styles.featureDescription}>
              Works perfectly on desktop and mobile devices 
              with adaptive layouts.
            </p>
          </div>

          <div style={styles.featureCard}>
            <div style={styles.featureIcon}>⚡</div>
            <h3 style={styles.featureTitle}>Loading States</h3>
            <p style={styles.featureDescription}>
              Smooth loading animations and disabled states 
              during authentication processes.
            </p>
          </div>
        </div>
      </div>

      {/* Code Example */}
      <div style={styles.codeSection}>
        <h2 style={styles.sectionTitle}>Usage Example</h2>
        <div style={styles.codeBlock}>
          <pre style={styles.code}>
{`import React from 'react';
import GoogleSignIn from './components/GoogleSignIn';

function App() {
  return (
    <div>
      <h1>My App</h1>
      <GoogleSignIn />
    </div>
  );
}

export default App;`}
          </pre>
        </div>
      </div>

      {/* Technical Details */}
      <div style={styles.techSection}>
        <h2 style={styles.sectionTitle}>Technical Implementation</h2>
        <div style={styles.techGrid}>
          <div style={styles.techItem}>
            <strong>Firebase v9 Modular SDK</strong>
            <p>Uses the latest Firebase authentication methods</p>
          </div>
          <div style={styles.techItem}>
            <strong>React Hooks</strong>
            <p>useState, useEffect for state management</p>
          </div>
          <div style={styles.techItem}>
            <strong>onAuthStateChanged</strong>
            <p>Real-time authentication state listener</p>
          </div>
          <div style={styles.techItem}>
            <strong>signInWithPopup</strong>
            <p>Google OAuth popup authentication</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div style={styles.footer}>
        <p style={styles.footerText}>
          Component Location: <code>src/components/GoogleSignIn.jsx</code>
        </p>
        <p style={styles.footerText}>
          Ready to use with your Firebase configuration!
        </p>
      </div>
    </div>
  );
};

const styles = {
  container: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    lineHeight: '1.6',
    color: '#333'
  },
  header: {
    textAlign: 'center',
    padding: '60px 20px',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white'
  },
  title: {
    fontSize: '48px',
    fontWeight: '700',
    margin: '0 0 16px 0'
  },
  subtitle: {
    fontSize: '20px',
    fontWeight: '300',
    margin: '0',
    opacity: '0.9'
  },
  demoSection: {
    padding: '60px 20px',
    backgroundColor: '#f8f9fa'
  },
  sectionTitle: {
    fontSize: '32px',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: '40px',
    color: '#2c3e50'
  },
  demoContainer: {
    display: 'flex',
    justifyContent: 'center'
  },
  featuresSection: {
    padding: '60px 20px',
    backgroundColor: 'white'
  },
  featuresGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: '30px',
    maxWidth: '1200px',
    margin: '0 auto'
  },
  featureCard: {
    textAlign: 'center',
    padding: '30px 20px',
    borderRadius: '12px',
    backgroundColor: '#f8f9fa',
    border: '1px solid #e9ecef'
  },
  featureIcon: {
    fontSize: '48px',
    marginBottom: '16px'
  },
  featureTitle: {
    fontSize: '20px',
    fontWeight: '600',
    marginBottom: '12px',
    color: '#2c3e50'
  },
  featureDescription: {
    fontSize: '16px',
    color: '#6c757d',
    margin: '0'
  },
  codeSection: {
    padding: '60px 20px',
    backgroundColor: '#2c3e50'
  },
  codeBlock: {
    maxWidth: '800px',
    margin: '0 auto',
    backgroundColor: '#34495e',
    borderRadius: '8px',
    overflow: 'hidden'
  },
  code: {
    color: '#ecf0f1',
    fontSize: '14px',
    padding: '30px',
    margin: '0',
    overflow: 'auto'
  },
  techSection: {
    padding: '60px 20px',
    backgroundColor: 'white'
  },
  techGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: '20px',
    maxWidth: '800px',
    margin: '0 auto'
  },
  techItem: {
    padding: '20px',
    borderLeft: '4px solid #667eea',
    backgroundColor: '#f8f9fa'
  },
  footer: {
    textAlign: 'center',
    padding: '40px 20px',
    backgroundColor: '#2c3e50',
    color: 'white'
  },
  footerText: {
    margin: '8px 0',
    fontSize: '16px'
  }
};

export default ComponentShowcase;
