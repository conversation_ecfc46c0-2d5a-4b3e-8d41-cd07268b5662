import React from 'react';

const FeatureComparison = () => {
  const features = [
    {
      category: "User Information Display",
      items: [
        { feature: "Profile Picture with Fallback", useState: "✅", hooks: "✅", description: "User's Google profile photo with default avatar fallback" },
        { feature: "Display Name", useState: "✅", hooks: "✅", description: "Full name from Google account" },
        { feature: "Email Address", useState: "✅", hooks: "✅", description: "Primary email address" },
        { feature: "Phone Number", useState: "✅", hooks: "✅", description: "Phone number if available" },
        { feature: "Email Verification Status", useState: "✅", hooks: "✅", description: "Visual indicator for verified emails" },
        { feature: "User ID Display", useState: "✅", hooks: "✅", description: "Truncated Firebase UID for security" },
        { feature: "Account Creation Date", useState: "✅", hooks: "✅", description: "When the account was first created" },
        { feature: "Last Sign-In Time", useState: "✅", hooks: "✅", description: "Most recent authentication timestamp" }
      ]
    },
    {
      category: "Session Tracking",
      items: [
        { feature: "Real-time Session Duration", useState: "✅", hooks: "✅", description: "Live tracking of current session time" },
        { feature: "Session Statistics", useState: "✅", hooks: "✅", description: "Custom metrics and activity tracking" },
        { feature: "Online Status Indicator", useState: "✅", hooks: "✅", description: "Visual indicator showing user is online" },
        { feature: "Activity Timestamps", useState: "✅", hooks: "✅", description: "Last activity and interaction tracking" }
      ]
    },
    {
      category: "State Management",
      items: [
        { feature: "Authentication State", useState: "Manual useState", hooks: "useAuthState hook", description: "How auth state is managed" },
        { feature: "Loading States", useState: "Manual useState", hooks: "Built-in hook", description: "Loading state management" },
        { feature: "Error Handling", useState: "Manual useState", hooks: "Built-in hook", description: "Error state management" },
        { feature: "Automatic Cleanup", useState: "Manual useEffect", hooks: "Automatic", description: "Memory management and cleanup" },
        { feature: "Code Complexity", useState: "Higher", hooks: "Lower", description: "Amount of boilerplate code required" }
      ]
    },
    {
      category: "UI/UX Features",
      items: [
        { feature: "Modern Design", useState: "✅", hooks: "✅", description: "Clean, professional styling" },
        { feature: "Responsive Layout", useState: "✅", hooks: "✅", description: "Works on desktop and mobile" },
        { feature: "Loading Animations", useState: "✅", hooks: "✅", description: "Smooth loading spinners" },
        { feature: "Status Badges", useState: "✅", hooks: "✅", description: "Verification and status indicators" },
        { feature: "Error Messages", useState: "✅", hooks: "✅", description: "User-friendly error display" },
        { feature: "Interactive Elements", useState: "✅", hooks: "✅", description: "Hover effects and transitions" }
      ]
    },
    {
      category: "Technical Implementation",
      items: [
        { feature: "Firebase v9 SDK", useState: "✅", hooks: "✅", description: "Uses latest Firebase modular SDK" },
        { feature: "TypeScript Support", useState: "Manual", hooks: "Built-in", description: "TypeScript compatibility" },
        { feature: "External Dependencies", useState: "None", hooks: "react-firebase-hooks", description: "Additional package requirements" },
        { feature: "Bundle Size Impact", useState: "Minimal", hooks: "Small increase", description: "Effect on application bundle size" },
        { feature: "Learning Curve", useState: "Standard React", hooks: "Library-specific", description: "Knowledge required to implement" }
      ]
    }
  ];

  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <h1 style={styles.title}>📊 Feature Comparison Matrix</h1>
        <p style={styles.subtitle}>
          Detailed comparison of useState vs react-firebase-hooks implementations
        </p>
      </div>

      <div style={styles.content}>
        {features.map((category, categoryIndex) => (
          <div key={categoryIndex} style={styles.categorySection}>
            <h2 style={styles.categoryTitle}>{category.category}</h2>
            
            <div style={styles.table}>
              <div style={styles.tableHeader}>
                <div style={styles.featureColumn}>Feature</div>
                <div style={styles.useStateColumn}>useState Approach</div>
                <div style={styles.hooksColumn}>react-firebase-hooks</div>
                <div style={styles.descriptionColumn}>Description</div>
              </div>
              
              {category.items.map((item, itemIndex) => (
                <div key={itemIndex} style={styles.tableRow}>
                  <div style={styles.featureCell}>
                    <strong>{item.feature}</strong>
                  </div>
                  <div style={styles.useStateCell}>
                    <span style={getStatusStyle(item.useState)}>{item.useState}</span>
                  </div>
                  <div style={styles.hooksCell}>
                    <span style={getStatusStyle(item.hooks)}>{item.hooks}</span>
                  </div>
                  <div style={styles.descriptionCell}>
                    {item.description}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Summary Section */}
        <div style={styles.summarySection}>
          <h2 style={styles.summaryTitle}>🎯 Quick Decision Guide</h2>
          
          <div style={styles.summaryGrid}>
            <div style={styles.summaryCard}>
              <h3 style={styles.summaryCardTitle}>Choose useState When:</h3>
              <ul style={styles.summaryList}>
                <li>🎯 You need full control over state logic</li>
                <li>📦 You want to minimize dependencies</li>
                <li>🔧 You need custom authentication flows</li>
                <li>📚 You're learning Firebase authentication</li>
                <li>⚡ You have specific performance requirements</li>
                <li>🎨 You want maximum customization flexibility</li>
              </ul>
            </div>
            
            <div style={styles.summaryCard}>
              <h3 style={styles.summaryCardTitle}>Choose react-firebase-hooks When:</h3>
              <ul style={styles.summaryList}>
                <li>🚀 You want rapid development</li>
                <li>📝 You prefer less boilerplate code</li>
                <li>🛡️ You need TypeScript support</li>
                <li>🏭 You're building production apps</li>
                <li>🧹 You want automatic cleanup</li>
                <li>✅ You trust battle-tested libraries</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to style status indicators
const getStatusStyle = (status) => {
  if (status === "✅") {
    return { color: '#10b981', fontWeight: 'bold' };
  } else if (status.includes("Manual") || status.includes("Higher")) {
    return { color: '#f59e0b', fontWeight: '500' };
  } else if (status.includes("Built-in") || status.includes("Automatic") || status.includes("Lower")) {
    return { color: '#10b981', fontWeight: '500' };
  }
  return { color: '#64748b', fontWeight: '500' };
};

const styles = {
  container: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    lineHeight: '1.6',
    color: '#333',
    backgroundColor: '#f8fafc',
    minHeight: '100vh'
  },
  header: {
    textAlign: 'center',
    padding: '40px 20px',
    backgroundColor: 'white',
    borderBottom: '1px solid #e2e8f0'
  },
  title: {
    fontSize: '36px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  subtitle: {
    fontSize: '18px',
    color: '#64748b',
    margin: '0'
  },
  content: {
    maxWidth: '1400px',
    margin: '0 auto',
    padding: '40px 20px'
  },
  categorySection: {
    marginBottom: '48px',
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '32px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e2e8f0'
  },
  categoryTitle: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 24px 0',
    paddingBottom: '12px',
    borderBottom: '2px solid #e2e8f0'
  },
  table: {
    width: '100%'
  },
  tableHeader: {
    display: 'grid',
    gridTemplateColumns: '2fr 1.5fr 1.5fr 2fr',
    gap: '16px',
    padding: '16px',
    backgroundColor: '#f1f5f9',
    borderRadius: '8px',
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: '8px'
  },
  tableRow: {
    display: 'grid',
    gridTemplateColumns: '2fr 1.5fr 1.5fr 2fr',
    gap: '16px',
    padding: '16px',
    borderBottom: '1px solid #f1f5f9',
    alignItems: 'center'
  },
  featureColumn: {
    fontSize: '14px',
    fontWeight: '600'
  },
  useStateColumn: {
    fontSize: '14px',
    fontWeight: '600',
    textAlign: 'center'
  },
  hooksColumn: {
    fontSize: '14px',
    fontWeight: '600',
    textAlign: 'center'
  },
  descriptionColumn: {
    fontSize: '14px',
    fontWeight: '600'
  },
  featureCell: {
    fontSize: '14px',
    color: '#1e293b'
  },
  useStateCell: {
    fontSize: '14px',
    textAlign: 'center'
  },
  hooksCell: {
    fontSize: '14px',
    textAlign: 'center'
  },
  descriptionCell: {
    fontSize: '13px',
    color: '#64748b',
    lineHeight: '1.4'
  },
  summarySection: {
    marginTop: '48px',
    padding: '32px',
    backgroundColor: 'white',
    borderRadius: '12px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e2e8f0'
  },
  summaryTitle: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 24px 0',
    textAlign: 'center'
  },
  summaryGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
    gap: '24px'
  },
  summaryCard: {
    padding: '24px',
    backgroundColor: '#f8fafc',
    borderRadius: '8px',
    border: '1px solid #e2e8f0'
  },
  summaryCardTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  summaryList: {
    margin: '0',
    padding: '0 0 0 16px',
    color: '#64748b'
  }
};

export default FeatureComparison;
