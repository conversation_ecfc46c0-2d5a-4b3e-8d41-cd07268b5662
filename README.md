# React Firebase Google Authentication

This project demonstrates how to implement Google Sign-In authentication in a React application using Firebase v9 modular SDK and react-firebase-hooks.

## Features

- Google Sign-In authentication
- Firebase v9 modular SDK
- react-firebase-hooks for state management
- Clean separation of concerns with context API
- Responsive UI components

## Setup Instructions

### 1. Firebase Project Setup

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select an existing one
3. Enable Authentication:
   - Go to Authentication > Sign-in method
   - Enable Google as a sign-in provider
   - Add your domain to authorized domains
4. Get your Firebase configuration:
   - Go to Project Settings > General
   - Scroll down to "Your apps" section
   - Click on the web app icon or create a new web app
   - Copy the Firebase configuration object

### 2. Configure Firebase

1. Open `src/firebase/config.js`
2. Replace the placeholder values with your actual Firebase configuration:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id",
  measurementId: "your-measurement-id" // Optional
};
```

### 3. Install Dependencies

Dependencies are already installed, but if you need to reinstall:

```bash
npm install
```

### 4. Run the Application

```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## Project Structure

```
src/
├── components/
│   ├── LoginButton.jsx      # Google Sign-In button component
│   └── UserProfile.jsx      # User profile display component
├── contexts/
│   └── AuthContext.jsx      # Authentication context provider
├── firebase/
│   ├── config.js           # Firebase configuration
│   └── auth.js             # Authentication service functions
├── App.jsx                 # Main application component
└── main.jsx               # Application entry point
```

## Key Components

### AuthContext
Provides authentication state and methods throughout the app using react-firebase-hooks.

### LoginButton
A styled button component that handles Google Sign-In with popup.

### UserProfile
Displays user information and provides sign-out functionality.

## Technologies Used

- React 18
- Vite
- Firebase v10 (v9 modular SDK)
- react-firebase-hooks v5.1.1

## Security Notes

- Never commit your actual Firebase configuration to version control
- Consider using environment variables for sensitive configuration
- Ensure your Firebase security rules are properly configured
- Add proper error handling for production use
