# ProtectedRoute Component Documentation

A comprehensive React component that protects routes by checking Firebase authentication status and redirecting unauthenticated users to a login page or custom fallback.

## 🔒 Overview

The ProtectedRoute component provides authentication protection for React applications using Firebase. It offers multiple implementation approaches and flexible configuration options.

## 📁 Available Implementations

### 1. ProtectedRoute.jsx (AuthContext Version)
**Uses existing AuthContext for authentication state**

```jsx
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';

<AuthProvider>
  <ProtectedRoute>
    <YourProtectedContent />
  </ProtectedRoute>
</AuthProvider>
```

**Features:**
- ✅ Uses existing AuthContext
- ✅ Consistent with app architecture
- ✅ Shared authentication state
- ✅ Custom fallback components
- ✅ Email verification support

### 2. ProtectedRouteFirebase.jsx (Direct Firebase)
**Works directly with Firebase auth without context dependency**

```jsx
import ProtectedRouteFirebase from './components/ProtectedRouteFirebase';

<ProtectedRouteFirebase>
  <YourProtectedContent />
</ProtectedRouteFirebase>
```

**Features:**
- ✅ No context dependency
- ✅ Direct Firebase integration
- ✅ Enhanced error handling
- ✅ Callback support
- ✅ Standalone implementation

### 3. ProtectedRouteHooks.jsx (react-firebase-hooks)
**Uses react-firebase-hooks for simplified state management**

```jsx
import ProtectedRouteHooks from './components/ProtectedRouteHooks';

<ProtectedRouteHooks>
  <YourProtectedContent />
</ProtectedRouteHooks>
```

**Features:**
- ✅ Built-in loading states
- ✅ Automatic error handling
- ✅ TypeScript support
- ✅ Minimal boilerplate
- ✅ Well-tested library

## 🛠️ Props API

### Common Props (All Versions)

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `React.ReactNode` | - | Content to render if authenticated |
| `fallback` | `React.ComponentType` | `undefined` | Custom component to render if not authenticated |
| `redirectTo` | `string` | `undefined` | URL to redirect to if not authenticated |
| `requireEmailVerification` | `boolean` | `false` | Whether to require email verification |

### Additional Props (Firebase Direct & Hooks)

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `onAuthStateChange` | `Function` | `undefined` | Callback when auth state changes |

## 📋 Usage Examples

### Basic Usage

```jsx
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      <ProtectedRoute>
        <Dashboard />
      </ProtectedRoute>
    </AuthProvider>
  );
}
```

### With Custom Fallback Component

```jsx
const CustomLogin = () => (
  <div>
    <h2>Please Sign In</h2>
    <LoginButton />
  </div>
);

<ProtectedRoute fallback={CustomLogin}>
  <ProtectedContent />
</ProtectedRoute>
```

### With Redirect

```jsx
<ProtectedRoute redirectTo="/login">
  <AdminPanel />
</ProtectedRoute>
```

### Requiring Email Verification

```jsx
<ProtectedRoute requireEmailVerification={true}>
  <VerifiedUsersOnly />
</ProtectedRoute>
```

### With Auth State Callback

```jsx
<ProtectedRouteFirebase 
  onAuthStateChange={(user) => {
    console.log('Auth changed:', user ? 'Signed in' : 'Signed out');
  }}
>
  <ProtectedContent />
</ProtectedRouteFirebase>
```

## 🌐 React Router Integration

### Basic Router Setup

```jsx
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route 
            path="/dashboard" 
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin" 
            element={
              <ProtectedRoute requireEmailVerification={true}>
                <AdminPanel />
              </ProtectedRoute>
            } 
          />
        </Routes>
      </BrowserRouter>
    </AuthProvider>
  );
}
```

### Advanced Router Setup with Nested Routes

```jsx
<Route 
  path="/app/*" 
  element={
    <ProtectedRoute>
      <Routes>
        <Route path="dashboard" element={<Dashboard />} />
        <Route path="profile" element={<Profile />} />
        <Route 
          path="admin/*" 
          element={
            <ProtectedRoute requireEmailVerification={true}>
              <AdminRoutes />
            </ProtectedRoute>
          } 
        />
      </Routes>
    </ProtectedRoute>
  } 
/>
```

## 🎯 Component States

### Loading State
- Shows while checking authentication status
- Displays loading spinner and message
- Prevents content flash before auth check

### Authenticated State
- User is signed in (and email verified if required)
- Renders children components
- Full access to protected content

### Unauthenticated State
- User is not signed in
- Shows fallback component, redirects, or default login prompt
- Blocks access to protected content

### Email Verification Required State
- User is signed in but email not verified
- Shows verification prompt with user info
- Blocks access until verification complete

### Error State
- Authentication error occurred
- Shows error message and retry options
- Provides debugging information

## 🔧 Customization

### Custom Loading Component

```jsx
const CustomLoading = () => (
  <div className="custom-loading">
    <Spinner />
    <p>Verifying your credentials...</p>
  </div>
);

// Modify ProtectedRoute component to accept loading prop
<ProtectedRoute loading={CustomLoading}>
  <ProtectedContent />
</ProtectedRoute>
```

### Custom Styling

```jsx
// Override default styles
const customStyles = {
  container: {
    backgroundColor: '#your-color',
    // ... other styles
  }
};

// Apply to ProtectedRoute component
```

### Multiple Protection Levels

```jsx
<ProtectedRoute>
  <div>
    <h1>Level 1: Basic Auth</h1>
    
    <ProtectedRoute requireEmailVerification={true}>
      <div>
        <h2>Level 2: Email Verified</h2>
        
        <ProtectedRoute fallback={AdminLoginRequired}>
          <div>
            <h3>Level 3: Admin Access</h3>
          </div>
        </ProtectedRoute>
      </div>
    </ProtectedRoute>
  </div>
</ProtectedRoute>
```

## ⚡ Performance Considerations

### Optimization Tips

1. **Minimize Re-renders**
   - Use React.memo for expensive child components
   - Optimize auth context to prevent unnecessary updates

2. **Loading States**
   - Show loading immediately to prevent content flash
   - Use skeleton screens for better UX

3. **Error Boundaries**
   - Wrap ProtectedRoute in error boundaries
   - Handle authentication errors gracefully

4. **Code Splitting**
   - Lazy load protected components
   - Reduce initial bundle size

```jsx
const LazyDashboard = React.lazy(() => import('./Dashboard'));

<ProtectedRoute>
  <Suspense fallback={<Loading />}>
    <LazyDashboard />
  </Suspense>
</ProtectedRoute>
```

## 🧪 Testing

### Unit Testing

```jsx
import { render, screen } from '@testing-library/react';
import ProtectedRoute from './ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';

// Mock Firebase auth
jest.mock('../firebase', () => ({
  auth: {},
}));

test('renders children when authenticated', () => {
  // Mock authenticated user
  const mockUser = { uid: '123', email: '<EMAIL>' };
  
  render(
    <AuthProvider>
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    </AuthProvider>
  );
  
  expect(screen.getByText('Protected Content')).toBeInTheDocument();
});

test('shows login prompt when not authenticated', () => {
  // Mock no user
  render(
    <AuthProvider>
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    </AuthProvider>
  );
  
  expect(screen.getByText(/access restricted/i)).toBeInTheDocument();
});
```

## 🔒 Security Best Practices

### 1. Server-Side Validation
- Always validate authentication on the server
- Don't rely solely on client-side protection
- Verify Firebase tokens on your backend

### 2. Token Management
- Handle token expiration gracefully
- Implement automatic token refresh
- Clear tokens on logout

### 3. Route Protection
- Protect sensitive routes at multiple levels
- Use email verification for critical actions
- Implement role-based access control

### 4. Error Handling
- Don't expose sensitive error information
- Log security events for monitoring
- Provide user-friendly error messages

## 📊 Comparison Matrix

| Feature | AuthContext | Firebase Direct | react-firebase-hooks |
|---------|-------------|-----------------|---------------------|
| **Setup Complexity** | Medium | Low | Low |
| **Dependencies** | AuthContext | None | react-firebase-hooks |
| **Bundle Size** | Small | Smallest | Small+ |
| **TypeScript** | Manual | Manual | Built-in |
| **Error Handling** | Custom | Enhanced | Built-in |
| **Loading States** | Custom | Enhanced | Built-in |
| **Flexibility** | High | Highest | Medium |
| **Maintenance** | Medium | High | Low |

## 🚀 Getting Started

1. **Choose Implementation**
   - Use AuthContext version for existing apps
   - Use Firebase Direct for new standalone components
   - Use react-firebase-hooks for rapid development

2. **Install Dependencies**
   ```bash
   npm install firebase
   # For hooks version:
   npm install react-firebase-hooks
   ```

3. **Configure Firebase**
   - Set up Firebase project
   - Enable authentication
   - Configure your app

4. **Implement ProtectedRoute**
   - Choose appropriate version
   - Wrap protected components
   - Test authentication flow

## 📁 File Structure

```
src/
├── components/
│   ├── ProtectedRoute.jsx           # AuthContext version
│   ├── ProtectedRouteFirebase.jsx   # Direct Firebase version
│   ├── ProtectedRouteHooks.jsx      # react-firebase-hooks version
│   ├── ProtectedContent.jsx         # Example protected content
│   └── ProtectedRouteDemo.jsx       # Comprehensive demo
├── contexts/
│   └── AuthContext.jsx              # Authentication context
├── examples/
│   └── ProtectedRouteExamples.jsx   # Usage examples
└── firebase.js                     # Firebase configuration
```

All ProtectedRoute implementations provide robust authentication protection with flexible configuration options. Choose the version that best fits your application architecture and requirements!
