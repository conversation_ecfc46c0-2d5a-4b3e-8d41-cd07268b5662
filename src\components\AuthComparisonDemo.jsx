import React, { useState } from 'react';
import EnhancedGoogleSignIn from './EnhancedGoogleSignIn';
import ReactFirebaseHooksSignIn from './ReactFirebaseHooksSignIn';

const AuthComparisonDemo = () => {
  const [currentView, setCurrentView] = useState('enhanced');

  const views = {
    enhanced: {
      component: EnhancedGoogleSignIn,
      title: 'useState Approach',
      description: 'Manual state management with useState and useEffect',
      features: [
        'Manual state management with useState',
        'Custom useEffect for auth state listening',
        'Full control over state updates',
        'Custom session tracking implementation',
        'Manual error handling',
        'Flexible and customizable'
      ],
      pros: [
        '✅ Full control over state logic',
        '✅ No additional dependencies',
        '✅ Custom features easy to implement',
        '✅ Better understanding of Firebase auth flow'
      ],
      cons: [
        '❌ More boilerplate code',
        '❌ Manual cleanup required',
        '❌ Potential for memory leaks if not handled properly'
      ]
    },
    hooks: {
      component: ReactFirebaseHooksSignIn,
      title: 'react-firebase-hooks',
      description: 'Simplified state management with react-firebase-hooks library',
      features: [
        'Built-in useAuthState hook',
        'Automatic loading and error states',
        'Built-in useSignInWithGoogle hook',
        'Automatic cleanup and memory management',
        'TypeScript support out of the box',
        'Simplified API with less boilerplate'
      ],
      pros: [
        '✅ Less boilerplate code',
        '✅ Automatic cleanup and memory management',
        '✅ Built-in loading and error states',
        '✅ TypeScript support',
        '✅ Well-tested and maintained'
      ],
      cons: [
        '❌ Additional dependency',
        '❌ Less control over internal state logic',
        '❌ Learning curve for the library API'
      ]
    }
  };

  const CurrentComponent = views[currentView].component;
  const currentViewData = views[currentView];

  return (
    <div style={styles.container}>
      {/* Header */}
      <div style={styles.header}>
        <h1 style={styles.title}>🔐 Authentication State Management Comparison</h1>
        <p style={styles.subtitle}>
          Compare useState vs react-firebase-hooks approaches for Firebase authentication
        </p>
        
        {/* View Selector */}
        <div style={styles.viewSelector}>
          {Object.entries(views).map(([key, { title }]) => (
            <button
              key={key}
              onClick={() => setCurrentView(key)}
              style={{
                ...styles.viewButton,
                ...(currentView === key ? styles.activeViewButton : {})
              }}
            >
              {title}
            </button>
          ))}
        </div>
      </div>

      {/* Current View Info */}
      <div style={styles.infoSection}>
        <div style={styles.infoCard}>
          <h3 style={styles.infoTitle}>{currentViewData.title}</h3>
          <p style={styles.infoDescription}>{currentViewData.description}</p>
          
          <div style={styles.featuresGrid}>
            <div style={styles.featureColumn}>
              <h4 style={styles.columnTitle}>📋 Features</h4>
              <ul style={styles.featureList}>
                {currentViewData.features.map((feature, index) => (
                  <li key={index} style={styles.featureItem}>{feature}</li>
                ))}
              </ul>
            </div>
            
            <div style={styles.featureColumn}>
              <h4 style={styles.columnTitle}>👍 Pros</h4>
              <ul style={styles.featureList}>
                {currentViewData.pros.map((pro, index) => (
                  <li key={index} style={styles.proItem}>{pro}</li>
                ))}
              </ul>
            </div>
            
            <div style={styles.featureColumn}>
              <h4 style={styles.columnTitle}>👎 Cons</h4>
              <ul style={styles.featureList}>
                {currentViewData.cons.map((con, index) => (
                  <li key={index} style={styles.conItem}>{con}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Live Demo */}
      <div style={styles.demoSection}>
        <h2 style={styles.demoTitle}>🚀 Live Demo</h2>
        <div style={styles.demoContainer}>
          <CurrentComponent />
        </div>
      </div>

      {/* Code Comparison */}
      <div style={styles.codeSection}>
        <h2 style={styles.codeTitle}>💻 Code Comparison</h2>
        <div style={styles.codeGrid}>
          <div style={styles.codeCard}>
            <h3 style={styles.codeCardTitle}>useState Approach</h3>
            <pre style={styles.codeBlock}>
{`// useState approach
const [user, setUser] = useState(null);
const [loading, setLoading] = useState(true);
const [error, setError] = useState(null);

useEffect(() => {
  const unsubscribe = onAuthStateChanged(auth, (user) => {
    setUser(user);
    setLoading(false);
  });
  return () => unsubscribe();
}, []);

const handleSignIn = async () => {
  try {
    await signInWithPopup(auth, googleProvider);
  } catch (error) {
    setError(error.message);
  }
};`}
            </pre>
          </div>
          
          <div style={styles.codeCard}>
            <h3 style={styles.codeCardTitle}>react-firebase-hooks</h3>
            <pre style={styles.codeBlock}>
{`// react-firebase-hooks approach
const [user, loading, error] = useAuthState(auth);
const [signInWithGoogle] = useSignInWithGoogle(auth);
const [signOut] = useSignOut(auth);

const handleSignIn = async () => {
  try {
    await signInWithGoogle();
  } catch (error) {
    // Error automatically handled by hook
  }
};

// That's it! Much simpler.`}
            </pre>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div style={styles.recommendationsSection}>
        <h2 style={styles.recommendationsTitle}>🎯 When to Use Each Approach</h2>
        <div style={styles.recommendationsGrid}>
          <div style={styles.recommendationCard}>
            <h3 style={styles.recommendationTitle}>Use useState When:</h3>
            <ul style={styles.recommendationList}>
              <li>You need full control over authentication state</li>
              <li>You want to minimize external dependencies</li>
              <li>You need custom state management logic</li>
              <li>You're building a learning project</li>
              <li>You have specific performance requirements</li>
            </ul>
          </div>
          
          <div style={styles.recommendationCard}>
            <h3 style={styles.recommendationTitle}>Use react-firebase-hooks When:</h3>
            <ul style={styles.recommendationList}>
              <li>You want to reduce boilerplate code</li>
              <li>You prefer battle-tested solutions</li>
              <li>You need TypeScript support</li>
              <li>You're building production applications quickly</li>
              <li>You want automatic cleanup and memory management</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div style={styles.footer}>
        <p style={styles.footerText}>
          Both approaches are valid and have their use cases. Choose based on your project requirements and team preferences.
        </p>
      </div>
    </div>
  );
};

const styles = {
  container: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    lineHeight: '1.6',
    color: '#333'
  },
  header: {
    textAlign: 'center',
    padding: '40px 20px',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white'
  },
  title: {
    fontSize: '36px',
    fontWeight: '700',
    margin: '0 0 12px 0'
  },
  subtitle: {
    fontSize: '18px',
    fontWeight: '300',
    margin: '0 0 32px 0',
    opacity: '0.9'
  },
  viewSelector: {
    display: 'flex',
    gap: '16px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  viewButton: {
    padding: '12px 24px',
    fontSize: '16px',
    fontWeight: '500',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    color: 'white',
    border: '2px solid rgba(255, 255, 255, 0.3)',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  activeViewButton: {
    backgroundColor: 'white',
    color: '#667eea',
    borderColor: 'white'
  },
  infoSection: {
    padding: '40px 20px',
    backgroundColor: '#f8fafc'
  },
  infoCard: {
    maxWidth: '1200px',
    margin: '0 auto',
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '32px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
  },
  infoTitle: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 8px 0'
  },
  infoDescription: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 24px 0'
  },
  featuresGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: '24px'
  },
  featureColumn: {
    padding: '20px',
    backgroundColor: '#f8fafc',
    borderRadius: '8px',
    border: '1px solid #e2e8f0'
  },
  columnTitle: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  featureList: {
    margin: '0',
    padding: '0',
    listStyle: 'none'
  },
  featureItem: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0 0 8px 0',
    paddingLeft: '16px',
    position: 'relative'
  },
  proItem: {
    fontSize: '14px',
    color: '#059669',
    margin: '0 0 8px 0'
  },
  conItem: {
    fontSize: '14px',
    color: '#dc2626',
    margin: '0 0 8px 0'
  },
  demoSection: {
    padding: '40px 20px',
    backgroundColor: 'white'
  },
  demoTitle: {
    fontSize: '28px',
    fontWeight: '600',
    textAlign: 'center',
    color: '#1e293b',
    margin: '0 0 32px 0'
  },
  demoContainer: {
    display: 'flex',
    justifyContent: 'center'
  },
  codeSection: {
    padding: '40px 20px',
    backgroundColor: '#1e293b'
  },
  codeTitle: {
    fontSize: '28px',
    fontWeight: '600',
    textAlign: 'center',
    color: 'white',
    margin: '0 0 32px 0'
  },
  codeGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
    gap: '24px',
    maxWidth: '1200px',
    margin: '0 auto'
  },
  codeCard: {
    backgroundColor: '#334155',
    borderRadius: '12px',
    overflow: 'hidden'
  },
  codeCardTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: 'white',
    margin: '0',
    padding: '16px 20px',
    backgroundColor: '#475569',
    borderBottom: '1px solid #64748b'
  },
  codeBlock: {
    color: '#e2e8f0',
    fontSize: '13px',
    padding: '20px',
    margin: '0',
    overflow: 'auto',
    lineHeight: '1.5'
  },
  recommendationsSection: {
    padding: '40px 20px',
    backgroundColor: '#f8fafc'
  },
  recommendationsTitle: {
    fontSize: '28px',
    fontWeight: '600',
    textAlign: 'center',
    color: '#1e293b',
    margin: '0 0 32px 0'
  },
  recommendationsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
    gap: '24px',
    maxWidth: '1000px',
    margin: '0 auto'
  },
  recommendationCard: {
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '24px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e2e8f0'
  },
  recommendationTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  recommendationList: {
    margin: '0',
    padding: '0 0 0 20px',
    color: '#64748b'
  },
  footer: {
    textAlign: 'center',
    padding: '40px 20px',
    backgroundColor: '#1e293b',
    color: 'white'
  },
  footerText: {
    fontSize: '16px',
    margin: '0',
    opacity: '0.8'
  }
};

export default AuthComparisonDemo;
