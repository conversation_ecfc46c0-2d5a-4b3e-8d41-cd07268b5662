import React, { useState, useEffect } from 'react';
import { signInWithPopup, signOut, onAuthStateChanged } from 'firebase/auth';
import { auth, googleProvider } from '../firebase';

const EnhancedLogoutComponent = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [signingIn, setSigningIn] = useState(false);
  const [signingOut, setSigningOut] = useState(false);
  const [error, setError] = useState(null);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [sessionStats, setSessionStats] = useState({
    signInTime: null,
    sessionDuration: 0,
    actionsCount: 0
  });

  // Listen for authentication state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setLoading(false);
      
      if (currentUser) {
        setSessionStats(prev => ({
          ...prev,
          signInTime: new Date(),
          sessionDuration: 0,
          actionsCount: 0
        }));
      } else {
        setSessionStats({
          signInTime: null,
          sessionDuration: 0,
          actionsCount: 0
        });
      }
    });

    return () => unsubscribe();
  }, []);

  // Update session duration
  useEffect(() => {
    if (user && sessionStats.signInTime) {
      const timer = setInterval(() => {
        setSessionStats(prev => ({
          ...prev,
          sessionDuration: Math.floor((Date.now() - prev.signInTime.getTime()) / 1000)
        }));
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [user, sessionStats.signInTime]);

  // Handle Google Sign-In
  const handleSignIn = async () => {
    setSigningIn(true);
    setError(null);
    
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;
      
      console.log('User signed in successfully:', {
        name: user.displayName,
        email: user.email,
        uid: user.uid
      });
      
    } catch (error) {
      console.error('Error signing in with Google:', error);
      setError(error.message);
    } finally {
      setSigningIn(false);
    }
  };

  // Handle Sign Out with confirmation
  const handleSignOutClick = () => {
    setShowLogoutConfirm(true);
  };

  // Confirm and execute sign out
  const confirmSignOut = async () => {
    setSigningOut(true);
    setError(null);
    
    try {
      await signOut(auth);
      console.log('User signed out successfully');
      setShowLogoutConfirm(false);
      
      // Track action
      setSessionStats(prev => ({
        ...prev,
        actionsCount: prev.actionsCount + 1
      }));
      
    } catch (error) {
      console.error('Error signing out:', error);
      setError(error.message);
    } finally {
      setSigningOut(false);
    }
  };

  // Cancel sign out
  const cancelSignOut = () => {
    setShowLogoutConfirm(false);
  };

  // Quick sign out without confirmation
  const quickSignOut = async () => {
    setSigningOut(true);
    setError(null);
    
    try {
      await signOut(auth);
      console.log('User signed out successfully (quick)');
    } catch (error) {
      console.error('Error signing out:', error);
      setError(error.message);
    } finally {
      setSigningOut(false);
    }
  };

  // Format duration
  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) return `${hours}h ${minutes}m ${secs}s`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };

  // Loading state
  if (loading) {
    return (
      <div style={styles.container}>
        <div style={styles.loadingCard}>
          <div style={styles.spinner}></div>
          <p style={styles.loadingText}>Checking authentication status...</p>
        </div>
      </div>
    );
  }

  // User is signed in - show profile with enhanced logout options
  if (user) {
    return (
      <div style={styles.container}>
        <div style={styles.profileCard}>
          <div style={styles.header}>
            <h2 style={styles.welcomeTitle}>Welcome, {user.displayName?.split(' ')[0] || 'User'}!</h2>
            <div style={styles.sessionInfo}>
              <span style={styles.sessionLabel}>Session: {formatDuration(sessionStats.sessionDuration)}</span>
            </div>
          </div>

          {/* User Profile */}
          <div style={styles.profileSection}>
            <img 
              src={user.photoURL || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA1MEMzNy44NSA1MCAyOCA0MC4xNSAyOCAyOEMyOCAxNS44NSAzNy44NSA2IDUwIDZDNjIuMTUgNiA3MiAxNS44NSA3MiAyOEM3MiA0MC4xNSA2Mi4xNSA1MCA1MCA1MFpNNTAgNTZDNjYuNjcgNTYgODAgNjIuMzMgODAgNzBWNzZINzJWNzBDNzIgNjYuNjcgNjMuMzMgNjAgNTAgNjBDMzYuNjcgNjAgMjggNjYuNjcgMjggNzBWNzZIMjBWNzBDMjAgNjIuMzMgMzMuMzMgNTYgNTAgNTZaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo='} 
              alt={user.displayName || 'User'}
              style={styles.profileImage}
            />
            <div style={styles.userInfo}>
              <h3 style={styles.userName}>{user.displayName || 'Anonymous User'}</h3>
              <p style={styles.userEmail}>{user.email}</p>
              <p style={styles.userDetail}>
                Signed in: {sessionStats.signInTime?.toLocaleTimeString()}
              </p>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div style={styles.errorMessage}>
              <span style={styles.errorIcon}>⚠️</span>
              <span>Error: {error}</span>
            </div>
          )}

          {/* Logout Confirmation Modal */}
          {showLogoutConfirm && (
            <div style={styles.modal}>
              <div style={styles.modalContent}>
                <h3 style={styles.modalTitle}>Confirm Sign Out</h3>
                <p style={styles.modalText}>
                  Are you sure you want to sign out? Your session will end and you'll need to sign in again.
                </p>
                <div style={styles.modalActions}>
                  <button 
                    onClick={cancelSignOut}
                    style={styles.cancelButton}
                    disabled={signingOut}
                  >
                    Cancel
                  </button>
                  <button 
                    onClick={confirmSignOut}
                    style={{
                      ...styles.confirmButton,
                      opacity: signingOut ? 0.6 : 1
                    }}
                    disabled={signingOut}
                  >
                    {signingOut ? '🔄 Signing out...' : '✅ Yes, Sign Out'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Logout Buttons */}
          <div style={styles.logoutSection}>
            <h4 style={styles.logoutTitle}>🚪 Sign Out Options</h4>
            <div style={styles.logoutButtons}>
              <button 
                onClick={handleSignOutClick}
                style={styles.primaryLogoutButton}
                disabled={signingOut}
              >
                🔒 Sign Out (with confirmation)
              </button>
              <button 
                onClick={quickSignOut}
                style={{
                  ...styles.quickLogoutButton,
                  opacity: signingOut ? 0.6 : 1
                }}
                disabled={signingOut}
              >
                {signingOut ? '🔄 Signing out...' : '⚡ Quick Sign Out'}
              </button>
            </div>
            <p style={styles.logoutHint}>
              💡 Use "Quick Sign Out" for immediate logout or "Sign Out" for confirmation dialog
            </p>
          </div>
        </div>
      </div>
    );
  }

  // User is not signed in - show sign in interface
  return (
    <div style={styles.container}>
      <div style={styles.signInCard}>
        <h2 style={styles.title}>🔐 Enhanced Logout Demo</h2>
        <p style={styles.description}>
          Sign in to test the enhanced logout functionality with confirmation dialogs and loading states
        </p>

        {error && (
          <div style={styles.errorMessage}>
            <span style={styles.errorIcon}>⚠️</span>
            <span>Error: {error}</span>
          </div>
        )}

        <button 
          onClick={handleSignIn}
          disabled={signingIn}
          style={{
            ...styles.signInButton,
            opacity: signingIn ? 0.6 : 1
          }}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" style={styles.googleIcon}>
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          {signingIn ? 'Signing in...' : 'Sign in with Google'}
        </button>

        <div style={styles.features}>
          <h4 style={styles.featuresTitle}>🎯 Enhanced Logout Features:</h4>
          <ul style={styles.featuresList}>
            <li>✅ Confirmation dialog for safe logout</li>
            <li>⚡ Quick logout option</li>
            <li>🔄 Loading states during sign out</li>
            <li>📊 Session duration tracking</li>
            <li>⚠️ Error handling and display</li>
            <li>🎨 Modern UI with smooth animations</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f1f5f9',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  loadingCard: {
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '40px',
    textAlign: 'center',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e2e8f0'
  },
  spinner: {
    width: '40px',
    height: '40px',
    border: '4px solid #f1f5f9',
    borderTop: '4px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 16px'
  },
  loadingText: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0'
  },
  signInCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '40px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '480px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  profileCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '32px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
    maxWidth: '600px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '24px',
    paddingBottom: '16px',
    borderBottom: '1px solid #e2e8f0'
  },
  welcomeTitle: {
    fontSize: '24px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0'
  },
  sessionInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  sessionLabel: {
    fontSize: '12px',
    color: '#64748b',
    backgroundColor: '#f1f5f9',
    padding: '4px 8px',
    borderRadius: '12px',
    fontWeight: '500'
  },
  profileSection: {
    display: 'flex',
    alignItems: 'center',
    gap: '20px',
    marginBottom: '32px',
    padding: '20px',
    backgroundColor: '#f8fafc',
    borderRadius: '12px'
  },
  profileImage: {
    width: '70px',
    height: '70px',
    borderRadius: '50%',
    objectFit: 'cover',
    border: '3px solid #e2e8f0'
  },
  userInfo: {
    flex: 1,
    textAlign: 'left'
  },
  userName: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 4px 0'
  },
  userEmail: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0 0 4px 0'
  },
  userDetail: {
    fontSize: '12px',
    color: '#94a3b8',
    margin: '0'
  },
  logoutSection: {
    marginTop: '24px',
    padding: '24px',
    backgroundColor: '#fef2f2',
    borderRadius: '12px',
    border: '1px solid #fecaca'
  },
  logoutTitle: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#dc2626',
    margin: '0 0 16px 0'
  },
  logoutButtons: {
    display: 'flex',
    gap: '12px',
    marginBottom: '12px',
    flexWrap: 'wrap'
  },
  primaryLogoutButton: {
    flex: 1,
    minWidth: '200px',
    padding: '12px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#dc2626',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  quickLogoutButton: {
    flex: 1,
    minWidth: '150px',
    padding: '12px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#f59e0b',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  logoutHint: {
    fontSize: '12px',
    color: '#7c2d12',
    margin: '0',
    fontStyle: 'italic'
  },
  modal: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '32px',
    maxWidth: '400px',
    width: '90%',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    textAlign: 'center'
  },
  modalTitle: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  modalText: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0 0 24px 0',
    lineHeight: '1.5'
  },
  modalActions: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center'
  },
  cancelButton: {
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#f1f5f9',
    color: '#64748b',
    border: '1px solid #e2e8f0',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  confirmButton: {
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#dc2626',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  title: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: '12px'
  },
  description: {
    fontSize: '16px',
    color: '#64748b',
    lineHeight: '1.6',
    margin: '0 0 32px 0'
  },
  signInButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '12px',
    padding: '16px 32px',
    fontSize: '16px',
    fontWeight: '500',
    backgroundColor: '#4285f4',
    color: 'white',
    border: 'none',
    borderRadius: '12px',
    transition: 'all 0.2s ease',
    width: '100%',
    marginBottom: '32px',
    cursor: 'pointer'
  },
  googleIcon: {
    flexShrink: 0
  },
  features: {
    textAlign: 'left',
    padding: '24px',
    backgroundColor: '#f8fafc',
    borderRadius: '12px',
    border: '1px solid #e2e8f0'
  },
  featuresTitle: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  featuresList: {
    margin: '0',
    padding: '0 0 0 16px',
    color: '#64748b'
  },
  errorMessage: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    backgroundColor: '#fef2f2',
    border: '1px solid #fecaca',
    borderRadius: '8px',
    padding: '12px',
    margin: '16px 0',
    color: '#dc2626'
  },
  errorIcon: {
    fontSize: '16px'
  }
};

export default EnhancedLogoutComponent;
