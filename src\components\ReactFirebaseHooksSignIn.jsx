import React, { useState, useEffect } from 'react';
import { useAuthState, useSignInWithGoogle, useSignOut } from 'react-firebase-hooks/auth';
import { auth } from '../firebase';

// Enhanced Google Sign-In component using react-firebase-hooks
const ReactFirebaseHooksSignIn = () => {
  // Using react-firebase-hooks for state management
  const [user, loading, error] = useAuthState(auth);
  const [signInWithGoogle, googleUser, googleLoading, googleError] = useSignInWithGoogle(auth);
  const [signOut, signOutLoading, signOutError] = useSignOut(auth);
  
  // Additional state for enhanced features
  const [sessionStats, setSessionStats] = useState({
    sessionStart: null,
    sessionDuration: 0,
    pageViews: 1,
    lastActivity: new Date()
  });

  // Track session statistics
  useEffect(() => {
    if (user && !sessionStats.sessionStart) {
      setSessionStats(prev => ({
        ...prev,
        sessionStart: new Date(),
        lastActivity: new Date()
      }));
    }
  }, [user, sessionStats.sessionStart]);

  // Update session duration
  useEffect(() => {
    if (user && sessionStats.sessionStart) {
      const timer = setInterval(() => {
        setSessionStats(prev => ({
          ...prev,
          sessionDuration: Math.floor((Date.now() - prev.sessionStart.getTime()) / 1000),
          lastActivity: new Date()
        }));
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [user, sessionStats.sessionStart]);

  // Handle sign in
  const handleSignIn = async () => {
    try {
      await signInWithGoogle();
    } catch (err) {
      console.error('Sign in error:', err);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      setSessionStats({
        sessionStart: null,
        sessionDuration: 0,
        pageViews: 1,
        lastActivity: new Date()
      });
    } catch (err) {
      console.error('Sign out error:', err);
    }
  };

  // Format duration helper
  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) return `${hours}h ${minutes}m ${secs}s`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };

  // Get user provider info
  const getUserProvider = () => {
    if (!user?.providerData?.length) return 'Unknown';
    return user.providerData[0].providerId === 'google.com' ? 'Google' : 'Other';
  };

  // Loading state
  if (loading) {
    return (
      <div style={styles.container}>
        <div style={styles.loadingCard}>
          <div style={styles.spinner}></div>
          <h3 style={styles.loadingTitle}>🔍 Checking Authentication</h3>
          <p style={styles.loadingText}>Using react-firebase-hooks...</p>
        </div>
      </div>
    );
  }

  // Error state
  const currentError = error || googleError || signOutError;
  
  // User is signed in - show enhanced profile
  if (user) {
    return (
      <div style={styles.container}>
        <div style={styles.profileCard}>
          {/* Header with react-firebase-hooks badge */}
          <div style={styles.header}>
            <div>
              <h2 style={styles.welcomeTitle}>
                Hello, {user.displayName?.split(' ')[0] || 'User'}! 🎉
              </h2>
              <div style={styles.hooksBadge}>
                <span style={styles.hooksIcon}>⚛️</span>
                react-firebase-hooks
              </div>
            </div>
            <div style={styles.statusIndicator}>
              <div style={styles.onlineStatus}></div>
              <span style={styles.statusText}>Online</span>
            </div>
          </div>

          {/* Enhanced User Profile */}
          <div style={styles.profileSection}>
            <div style={styles.avatarWrapper}>
              <img 
                src={user.photoURL || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA1MEMzNy44NSA1MCAyOCA0MC4xNSAyOCAyOEMyOCAxNS44NSAzNy44NSA2IDUwIDZDNjIuMTUgNiA3MiAxNS44NSA3MiAyOEM3MiA0MC4xNSA2Mi4xNSA1MCA1MCA1MFpNNTAgNTZDNjYuNjcgNTYgODAgNjIuMzMgODAgNzBWNzZINzJWNzBDNzIgNjYuNjcgNjMuMzMgNjAgNTAgNjBDMzYuNjcgNjAgMjggNjYuNjcgMjggNzBWNzZIMjBWNzBDMjAgNjIuMzMgMzMuMzMgNTYgNTAgNTZaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo='} 
                alt={user.displayName || 'User'}
                style={styles.profileImage}
              />
              {user.emailVerified && (
                <div style={styles.verifiedBadge}>✓</div>
              )}
            </div>
            
            <div style={styles.userDetails}>
              <h3 style={styles.userName}>{user.displayName || 'Anonymous User'}</h3>
              <div style={styles.userMeta}>
                <div style={styles.metaItem}>
                  <span style={styles.metaIcon}>📧</span>
                  <span style={styles.metaText}>{user.email}</span>
                </div>
                {user.phoneNumber && (
                  <div style={styles.metaItem}>
                    <span style={styles.metaIcon}>📱</span>
                    <span style={styles.metaText}>{user.phoneNumber}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Statistics Dashboard */}
          <div style={styles.statsSection}>
            <h4 style={styles.statsTitle}>📊 Session Statistics</h4>
            <div style={styles.statsGrid}>
              <div style={styles.statCard}>
                <div style={styles.statIcon}>⏱️</div>
                <div style={styles.statContent}>
                  <span style={styles.statLabel}>Session Time</span>
                  <span style={styles.statValue}>{formatDuration(sessionStats.sessionDuration)}</span>
                </div>
              </div>
              
              <div style={styles.statCard}>
                <div style={styles.statIcon}>🔐</div>
                <div style={styles.statContent}>
                  <span style={styles.statLabel}>Provider</span>
                  <span style={styles.statValue}>{getUserProvider()}</span>
                </div>
              </div>
              
              <div style={styles.statCard}>
                <div style={styles.statIcon}>📅</div>
                <div style={styles.statContent}>
                  <span style={styles.statLabel}>Member Since</span>
                  <span style={styles.statValue}>
                    {new Date(user.metadata.creationTime).toLocaleDateString()}
                  </span>
                </div>
              </div>
              
              <div style={styles.statCard}>
                <div style={styles.statIcon}>🕐</div>
                <div style={styles.statContent}>
                  <span style={styles.statLabel}>Last Sign In</span>
                  <span style={styles.statValue}>
                    {new Date(user.metadata.lastSignInTime).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Account Information */}
          <div style={styles.accountSection}>
            <h4 style={styles.accountTitle}>🔒 Account Information</h4>
            <div style={styles.accountGrid}>
              <div style={styles.accountItem}>
                <span style={styles.accountLabel}>User ID:</span>
                <span style={styles.accountValue}>{user.uid.substring(0, 12)}...</span>
              </div>
              <div style={styles.accountItem}>
                <span style={styles.accountLabel}>Email Verified:</span>
                <span style={{
                  ...styles.accountValue,
                  color: user.emailVerified ? '#10b981' : '#ef4444'
                }}>
                  {user.emailVerified ? '✅ Verified' : '❌ Not Verified'}
                </span>
              </div>
              <div style={styles.accountItem}>
                <span style={styles.accountLabel}>Providers:</span>
                <span style={styles.accountValue}>
                  {user.providerData.map(p => p.providerId).join(', ')}
                </span>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {currentError && (
            <div style={styles.errorMessage}>
              <span style={styles.errorIcon}>⚠️</span>
              <span>Error: {currentError.message}</span>
            </div>
          )}

          {/* Action Buttons */}
          <div style={styles.actionSection}>
            <button 
              onClick={handleSignOut}
              disabled={signOutLoading}
              style={{
                ...styles.signOutButton,
                opacity: signOutLoading ? 0.6 : 1
              }}
            >
              {signOutLoading ? '🔄 Signing out...' : '🚪 Sign Out'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User is not signed in - show sign in interface
  return (
    <div style={styles.container}>
      <div style={styles.signInCard}>
        <div style={styles.signInHeader}>
          <h2 style={styles.title}>🔐 react-firebase-hooks Demo</h2>
          <div style={styles.hooksBadge}>
            <span style={styles.hooksIcon}>⚛️</span>
            Powered by react-firebase-hooks
          </div>
          <p style={styles.description}>
            Experience seamless authentication with react-firebase-hooks library
          </p>
        </div>

        {currentError && (
          <div style={styles.errorMessage}>
            <span style={styles.errorIcon}>⚠️</span>
            <span>Error: {currentError.message}</span>
          </div>
        )}

        <button 
          onClick={handleSignIn}
          disabled={googleLoading}
          style={{
            ...styles.signInButton,
            opacity: googleLoading ? 0.6 : 1
          }}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" style={styles.googleIcon}>
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          {googleLoading ? 'Signing in...' : 'Sign in with Google'}
        </button>

        <div style={styles.featuresSection}>
          <h4 style={styles.featuresTitle}>✨ react-firebase-hooks Features:</h4>
          <ul style={styles.featuresList}>
            <li>🎯 Simplified state management</li>
            <li>🔄 Automatic re-renders on auth changes</li>
            <li>⚡ Built-in loading and error states</li>
            <li>🛡️ TypeScript support</li>
            <li>📊 Enhanced session tracking</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

// Enhanced styles for react-firebase-hooks version
const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#0f172a',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  loadingCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '40px',
    textAlign: 'center',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    border: '1px solid #e2e8f0'
  },
  spinner: {
    width: '40px',
    height: '40px',
    border: '4px solid #f1f5f9',
    borderTop: '4px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 20px'
  },
  loadingTitle: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 8px 0'
  },
  loadingText: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0'
  },
  signInCard: {
    backgroundColor: 'white',
    borderRadius: '24px',
    padding: '40px',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
    maxWidth: '480px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  profileCard: {
    backgroundColor: 'white',
    borderRadius: '24px',
    padding: '32px',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    maxWidth: '700px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '32px',
    paddingBottom: '20px',
    borderBottom: '2px solid #f1f5f9'
  },
  welcomeTitle: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  hooksBadge: {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '6px',
    backgroundColor: '#dbeafe',
    color: '#1e40af',
    padding: '6px 12px',
    borderRadius: '20px',
    fontSize: '12px',
    fontWeight: '600',
    border: '1px solid #bfdbfe'
  },
  hooksIcon: {
    fontSize: '14px'
  },
  statusIndicator: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  onlineStatus: {
    width: '12px',
    height: '12px',
    backgroundColor: '#10b981',
    borderRadius: '50%',
    animation: 'pulse 2s infinite'
  },
  statusText: {
    fontSize: '14px',
    color: '#10b981',
    fontWeight: '500'
  },
  profileSection: {
    display: 'flex',
    alignItems: 'center',
    gap: '24px',
    marginBottom: '32px',
    padding: '24px',
    backgroundColor: '#f8fafc',
    borderRadius: '16px',
    border: '1px solid #e2e8f0'
  },
  avatarWrapper: {
    position: 'relative'
  },
  profileImage: {
    width: '90px',
    height: '90px',
    borderRadius: '50%',
    objectFit: 'cover',
    border: '4px solid #e2e8f0',
    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: '4px',
    right: '4px',
    backgroundColor: '#10b981',
    color: 'white',
    borderRadius: '50%',
    width: '28px',
    height: '28px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '14px',
    fontWeight: 'bold',
    border: '3px solid white',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
  },
  userDetails: {
    flex: 1,
    textAlign: 'left'
  },
  userName: {
    fontSize: '24px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  userMeta: {
    display: 'flex',
    flexDirection: 'column',
    gap: '6px'
  },
  metaItem: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  metaIcon: {
    fontSize: '14px'
  },
  metaText: {
    fontSize: '14px',
    color: '#64748b'
  },
  statsSection: {
    marginBottom: '32px'
  },
  statsTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  statsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(160px, 1fr))',
    gap: '16px'
  },
  statCard: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    padding: '16px',
    backgroundColor: '#f1f5f9',
    borderRadius: '12px',
    border: '1px solid #e2e8f0'
  },
  statIcon: {
    fontSize: '20px'
  },
  statContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: '2px'
  },
  statLabel: {
    fontSize: '11px',
    color: '#64748b',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  },
  statValue: {
    fontSize: '14px',
    color: '#1e293b',
    fontWeight: '600'
  },
  accountSection: {
    marginBottom: '32px'
  },
  accountTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  accountGrid: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    padding: '20px',
    backgroundColor: '#f8fafc',
    borderRadius: '12px',
    border: '1px solid #e2e8f0'
  },
  accountItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  accountLabel: {
    fontSize: '14px',
    color: '#64748b',
    fontWeight: '500'
  },
  accountValue: {
    fontSize: '14px',
    color: '#1e293b',
    fontWeight: '600'
  },
  actionSection: {
    display: 'flex',
    justifyContent: 'center'
  },
  signOutButton: {
    padding: '14px 32px',
    fontSize: '16px',
    fontWeight: '600',
    backgroundColor: '#ef4444',
    color: 'white',
    border: 'none',
    borderRadius: '12px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    minWidth: '160px'
  },
  signInHeader: {
    marginBottom: '32px'
  },
  title: {
    fontSize: '32px',
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: '16px'
  },
  description: {
    fontSize: '16px',
    color: '#64748b',
    lineHeight: '1.6',
    margin: '16px 0 0 0'
  },
  signInButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '12px',
    padding: '16px 32px',
    fontSize: '16px',
    fontWeight: '600',
    backgroundColor: '#4285f4',
    color: 'white',
    border: 'none',
    borderRadius: '12px',
    transition: 'all 0.2s ease',
    width: '100%',
    marginBottom: '32px',
    cursor: 'pointer'
  },
  googleIcon: {
    flexShrink: 0
  },
  featuresSection: {
    textAlign: 'left',
    padding: '24px',
    backgroundColor: '#f8fafc',
    borderRadius: '12px',
    border: '1px solid #e2e8f0'
  },
  featuresTitle: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  featuresList: {
    margin: '0',
    padding: '0',
    listStyle: 'none',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  errorMessage: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    backgroundColor: '#fef2f2',
    border: '1px solid #fecaca',
    borderRadius: '8px',
    padding: '12px',
    margin: '16px 0',
    color: '#dc2626'
  },
  errorIcon: {
    fontSize: '16px'
  }
};

export default ReactFirebaseHooksSignIn;
