import React, { useState, useEffect } from 'react';
import { signInWithPopup, signOut, onAuthStateChanged } from 'firebase/auth';
import { auth, googleProvider } from '../firebase';

// Enhanced Google Sign-In component using useState for state management
const EnhancedGoogleSignIn = () => {
  // State management using useState
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [signingIn, setSigningIn] = useState(false);
  const [error, setError] = useState(null);
  const [userStats, setUserStats] = useState({
    signInCount: 0,
    lastActivity: null,
    sessionDuration: 0
  });

  // Listen for authentication state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setLoading(false);
      
      if (currentUser) {
        // Update user statistics
        setUserStats(prev => ({
          ...prev,
          signInCount: prev.signInCount + 1,
          lastActivity: new Date(),
          sessionDuration: 0
        }));
        
        // Start session timer
        const sessionStart = Date.now();
        const timer = setInterval(() => {
          setUserStats(prev => ({
            ...prev,
            sessionDuration: Math.floor((Date.now() - sessionStart) / 1000)
          }));
        }, 1000);
        
        return () => clearInterval(timer);
      } else {
        // Reset stats when user signs out
        setUserStats({
          signInCount: 0,
          lastActivity: null,
          sessionDuration: 0
        });
      }
    });

    return () => unsubscribe();
  }, []);

  // Handle Google Sign-In
  const handleSignIn = async () => {
    setSigningIn(true);
    setError(null);
    
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;
      
      console.log('User signed in successfully:', {
        name: user.displayName,
        email: user.email,
        photoURL: user.photoURL,
        uid: user.uid,
        emailVerified: user.emailVerified,
        phoneNumber: user.phoneNumber,
        providerData: user.providerData
      });
      
    } catch (error) {
      console.error('Error signing in with Google:', error);
      setError(error.message);
    } finally {
      setSigningIn(false);
    }
  };

  // Handle Sign Out
  const handleSignOut = async () => {
    try {
      await signOut(auth);
      console.log('User signed out successfully');
    } catch (error) {
      console.error('Error signing out:', error);
      setError(error.message);
    }
  };

  // Format session duration
  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Loading state
  if (loading) {
    return (
      <div style={styles.container}>
        <div style={styles.loadingSpinner}>
          <div style={styles.spinner}></div>
          <p style={styles.loadingText}>Checking authentication status...</p>
        </div>
      </div>
    );
  }

  // User is signed in - show enhanced profile
  if (user) {
    return (
      <div style={styles.container}>
        <div style={styles.profileCard}>
          <div style={styles.header}>
            <h2 style={styles.welcomeTitle}>Welcome back, {user.displayName?.split(' ')[0] || 'User'}! 👋</h2>
            <div style={styles.statusBadge}>
              <span style={styles.statusDot}></span>
              Online
            </div>
          </div>
          
          {/* Enhanced User Profile Section */}
          <div style={styles.profileSection}>
            <div style={styles.avatarContainer}>
              <img 
                src={user.photoURL || '/default-avatar.png'} 
                alt={user.displayName || 'User'}
                style={styles.profileImage}
                onError={(e) => {
                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA1MEMzNy44NSA1MCAyOCA0MC4xNSAyOCAyOEMyOCAxNS44NSAzNy44NSA2IDUwIDZDNjIuMTUgNiA3MiAxNS44NSA3MiAyOEM3MiA0MC4xNSA2Mi4xNSA1MCA1MCA1MFpNNTAgNTZDNjYuNjcgNTYgODAgNjIuMzMgODAgNzBWNzZINzJWNzBDNzIgNjYuNjcgNjMuMzMgNjAgNTAgNjBDMzYuNjcgNjAgMjggNjYuNjcgMjggNzBWNzZIMjBWNzBDMjAgNjIuMzMgMzMuMzMgNTYgNTAgNTZaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo=';
                }}
              />
              {user.emailVerified && (
                <div style={styles.verifiedBadge} title="Email Verified">✓</div>
              )}
            </div>
            
            <div style={styles.userInfo}>
              <h3 style={styles.userName}>{user.displayName || 'Anonymous User'}</h3>
              <p style={styles.userEmail}>
                <span style={styles.emailIcon}>📧</span>
                {user.email}
              </p>
              {user.phoneNumber && (
                <p style={styles.userPhone}>
                  <span style={styles.phoneIcon}>📱</span>
                  {user.phoneNumber}
                </p>
              )}
            </div>
          </div>

          {/* User Details Grid */}
          <div style={styles.detailsGrid}>
            <div style={styles.detailCard}>
              <div style={styles.detailIcon}>🆔</div>
              <div style={styles.detailContent}>
                <span style={styles.detailLabel}>User ID</span>
                <span style={styles.detailValue}>{user.uid.substring(0, 8)}...</span>
              </div>
            </div>
            
            <div style={styles.detailCard}>
              <div style={styles.detailIcon}>🕒</div>
              <div style={styles.detailContent}>
                <span style={styles.detailLabel}>Last Sign In</span>
                <span style={styles.detailValue}>
                  {new Date(user.metadata.lastSignInTime).toLocaleDateString()}
                </span>
              </div>
            </div>
            
            <div style={styles.detailCard}>
              <div style={styles.detailIcon}>⏱️</div>
              <div style={styles.detailContent}>
                <span style={styles.detailLabel}>Session Time</span>
                <span style={styles.detailValue}>{formatDuration(userStats.sessionDuration)}</span>
              </div>
            </div>
            
            <div style={styles.detailCard}>
              <div style={styles.detailIcon}>🔐</div>
              <div style={styles.detailContent}>
                <span style={styles.detailLabel}>Provider</span>
                <span style={styles.detailValue}>Google</span>
              </div>
            </div>
          </div>

          {/* Account Status */}
          <div style={styles.statusSection}>
            <div style={styles.statusItem}>
              <span style={styles.statusLabel}>Email Verified:</span>
              <span style={{
                ...styles.statusValue,
                color: user.emailVerified ? '#28a745' : '#dc3545'
              }}>
                {user.emailVerified ? '✅ Yes' : '❌ No'}
              </span>
            </div>
            <div style={styles.statusItem}>
              <span style={styles.statusLabel}>Account Created:</span>
              <span style={styles.statusValue}>
                {new Date(user.metadata.creationTime).toLocaleDateString()}
              </span>
            </div>
          </div>

          {error && (
            <div style={styles.errorMessage}>
              <p>⚠️ Error: {error}</p>
            </div>
          )}

          <div style={styles.actionButtons}>
            <button 
              onClick={handleSignOut}
              style={styles.signOutButton}
            >
              🚪 Sign Out
            </button>
            <button 
              onClick={() => window.location.reload()}
              style={styles.refreshButton}
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User is not signed in - show sign in button
  return (
    <div style={styles.container}>
      <div style={styles.signInCard}>
        <div style={styles.signInHeader}>
          <h2 style={styles.title}>🔐 Enhanced Google Sign-In</h2>
          <p style={styles.description}>
            Sign in with your Google account to access enhanced user profile features
          </p>
        </div>

        {error && (
          <div style={styles.errorMessage}>
            <p>⚠️ Error: {error}</p>
          </div>
        )}

        <button 
          onClick={handleSignIn}
          disabled={signingIn}
          style={{
            ...styles.signInButton,
            opacity: signingIn ? 0.6 : 1,
            cursor: signingIn ? 'not-allowed' : 'pointer'
          }}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" style={styles.googleIcon}>
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          {signingIn ? 'Signing in...' : 'Sign in with Google'}
        </button>

        <div style={styles.features}>
          <h4 style={styles.featuresTitle}>Enhanced Features:</h4>
          <ul style={styles.featuresList}>
            <li>✨ Real-time session tracking</li>
            <li>📊 User statistics and activity</li>
            <li>🔒 Account verification status</li>
            <li>📱 Enhanced profile display</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

// Enhanced styles with modern design
const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f8fafc',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  loadingSpinner: {
    textAlign: 'center'
  },
  spinner: {
    width: '40px',
    height: '40px',
    border: '4px solid #f1f5f9',
    borderTop: '4px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 16px'
  },
  loadingText: {
    fontSize: '16px',
    color: '#64748b'
  },
  signInCard: {
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '40px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '450px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  profileCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '32px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
    maxWidth: '600px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '24px',
    paddingBottom: '16px',
    borderBottom: '1px solid #e2e8f0'
  },
  welcomeTitle: {
    fontSize: '24px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0'
  },
  statusBadge: {
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    backgroundColor: '#dcfce7',
    color: '#166534',
    padding: '4px 12px',
    borderRadius: '20px',
    fontSize: '12px',
    fontWeight: '500'
  },
  statusDot: {
    width: '8px',
    height: '8px',
    backgroundColor: '#22c55e',
    borderRadius: '50%'
  },
  profileSection: {
    display: 'flex',
    alignItems: 'center',
    gap: '20px',
    marginBottom: '24px',
    padding: '20px',
    backgroundColor: '#f8fafc',
    borderRadius: '12px'
  },
  avatarContainer: {
    position: 'relative'
  },
  profileImage: {
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    objectFit: 'cover',
    border: '4px solid #e2e8f0',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: '0',
    right: '0',
    backgroundColor: '#22c55e',
    color: 'white',
    borderRadius: '50%',
    width: '24px',
    height: '24px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '12px',
    fontWeight: 'bold',
    border: '2px solid white'
  },
  userInfo: {
    flex: 1,
    textAlign: 'left'
  },
  userName: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 8px 0'
  },
  userEmail: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0 0 4px 0',
    display: 'flex',
    alignItems: 'center',
    gap: '6px'
  },
  userPhone: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0',
    display: 'flex',
    alignItems: 'center',
    gap: '6px'
  },
  emailIcon: {
    fontSize: '12px'
  },
  phoneIcon: {
    fontSize: '12px'
  },
  detailsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(140px, 1fr))',
    gap: '12px',
    marginBottom: '24px'
  },
  detailCard: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '12px',
    backgroundColor: '#f1f5f9',
    borderRadius: '8px',
    border: '1px solid #e2e8f0'
  },
  detailIcon: {
    fontSize: '16px'
  },
  detailContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: '2px'
  },
  detailLabel: {
    fontSize: '10px',
    color: '#64748b',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  },
  detailValue: {
    fontSize: '12px',
    color: '#1e293b',
    fontWeight: '600'
  },
  statusSection: {
    display: 'flex',
    justifyContent: 'space-between',
    padding: '16px',
    backgroundColor: '#f8fafc',
    borderRadius: '8px',
    marginBottom: '24px'
  },
  statusItem: {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px'
  },
  statusLabel: {
    fontSize: '12px',
    color: '#64748b',
    fontWeight: '500'
  },
  statusValue: {
    fontSize: '14px',
    fontWeight: '600'
  },
  actionButtons: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center'
  },
  signOutButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#ef4444',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    flex: 1
  },
  refreshButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#6b7280',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    flex: 1
  },
  signInHeader: {
    marginBottom: '32px'
  },
  title: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: '12px'
  },
  description: {
    fontSize: '16px',
    color: '#64748b',
    lineHeight: '1.6',
    margin: '0'
  },
  signInButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '12px',
    padding: '16px 32px',
    fontSize: '16px',
    fontWeight: '500',
    backgroundColor: '#4285f4',
    color: 'white',
    border: 'none',
    borderRadius: '12px',
    transition: 'all 0.2s ease',
    width: '100%',
    marginBottom: '24px'
  },
  googleIcon: {
    flexShrink: 0
  },
  features: {
    textAlign: 'left',
    padding: '20px',
    backgroundColor: '#f8fafc',
    borderRadius: '8px'
  },
  featuresTitle: {
    fontSize: '14px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  featuresList: {
    margin: '0',
    padding: '0',
    listStyle: 'none'
  },
  errorMessage: {
    backgroundColor: '#fef2f2',
    border: '1px solid #fecaca',
    borderRadius: '8px',
    padding: '12px',
    margin: '16px 0',
    color: '#dc2626'
  }
};

export default EnhancedGoogleSignIn;
