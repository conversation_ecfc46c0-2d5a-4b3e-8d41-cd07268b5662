import React, { useState } from 'react';
import EnhancedGoogleSignIn from './components/EnhancedGoogleSignIn';
import ReactFirebaseHooksSignIn from './components/ReactFirebaseHooksSignIn';
import AuthComparisonDemo from './components/AuthComparisonDemo';
import GoogleSignIn from './components/GoogleSignIn';
import './App.css';

const EnhancedAuthDemo = () => {
  const [currentDemo, setCurrentDemo] = useState('comparison');

  const demos = {
    comparison: {
      component: AuthComparisonDemo,
      title: 'State Management Comparison',
      description: 'Compare useState vs react-firebase-hooks approaches',
      icon: '⚖️'
    },
    enhanced: {
      component: EnhancedGoogleSignIn,
      title: 'Enhanced useState Version',
      description: 'Advanced features with manual state management',
      icon: '🚀'
    },
    hooks: {
      component: ReactFirebaseHooksSignIn,
      title: 'react-firebase-hooks Version',
      description: 'Simplified state management with hooks library',
      icon: '⚛️'
    },
    original: {
      component: GoogleSignIn,
      title: 'Original Component',
      description: 'Basic implementation for reference',
      icon: '📝'
    }
  };

  const CurrentComponent = demos[currentDemo].component;
  const currentDemoData = demos[currentDemo];

  return (
    <div style={styles.container}>
      {/* Navigation Header */}
      <div style={styles.navigation}>
        <div style={styles.navContent}>
          <h1 style={styles.navTitle}>🔐 Enhanced Firebase Authentication Demo</h1>
          <p style={styles.navSubtitle}>
            Extended login components with enhanced user display and state management
          </p>
          
          <div style={styles.demoSelector}>
            {Object.entries(demos).map(([key, { title, icon }]) => (
              <button
                key={key}
                onClick={() => setCurrentDemo(key)}
                style={{
                  ...styles.demoButton,
                  ...(currentDemo === key ? styles.activeDemoButton : {})
                }}
              >
                <span style={styles.demoIcon}>{icon}</span>
                <span style={styles.demoButtonText}>{title}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Current Demo Info */}
      <div style={styles.infoBar}>
        <div style={styles.infoContent}>
          <div style={styles.currentDemo}>
            <span style={styles.currentDemoIcon}>{currentDemoData.icon}</span>
            <div style={styles.currentDemoText}>
              <h3 style={styles.currentDemoTitle}>{currentDemoData.title}</h3>
              <p style={styles.currentDemoDescription}>{currentDemoData.description}</p>
            </div>
          </div>
          
          <div style={styles.features}>
            <h4 style={styles.featuresTitle}>✨ Enhanced Features:</h4>
            <div style={styles.featuresTags}>
              <span style={styles.featureTag}>Real-time session tracking</span>
              <span style={styles.featureTag}>Enhanced user profile</span>
              <span style={styles.featureTag}>Account verification status</span>
              <span style={styles.featureTag}>Modern UI design</span>
              <span style={styles.featureTag}>Error handling</span>
              <span style={styles.featureTag}>Loading states</span>
            </div>
          </div>
        </div>
      </div>

      {/* Demo Component */}
      <div style={styles.demoArea}>
        <CurrentComponent />
      </div>

      {/* Implementation Summary */}
      <div style={styles.summarySection}>
        <div style={styles.summaryContent}>
          <h2 style={styles.summaryTitle}>📋 Implementation Summary</h2>
          
          <div style={styles.summaryGrid}>
            <div style={styles.summaryCard}>
              <h3 style={styles.summaryCardTitle}>🎯 User Display Enhancements</h3>
              <ul style={styles.summaryList}>
                <li>✅ User name, email, and profile photo</li>
                <li>✅ Account verification status</li>
                <li>✅ Session duration tracking</li>
                <li>✅ Last sign-in information</li>
                <li>✅ Provider information</li>
                <li>✅ Account creation date</li>
              </ul>
            </div>
            
            <div style={styles.summaryCard}>
              <h3 style={styles.summaryCardTitle}>⚛️ State Management Options</h3>
              <ul style={styles.summaryList}>
                <li>🔧 <strong>useState:</strong> Manual control, custom logic</li>
                <li>🎣 <strong>react-firebase-hooks:</strong> Simplified API</li>
                <li>📊 Both track authentication state changes</li>
                <li>🔄 Real-time updates on auth changes</li>
                <li>⚡ Loading and error state handling</li>
                <li>🧹 Automatic cleanup and memory management</li>
              </ul>
            </div>
            
            <div style={styles.summaryCard}>
              <h3 style={styles.summaryCardTitle}>🎨 UI/UX Improvements</h3>
              <ul style={styles.summaryList}>
                <li>🎯 Modern, responsive design</li>
                <li>📱 Mobile-friendly layouts</li>
                <li>🌈 Enhanced visual feedback</li>
                <li>⏱️ Real-time session timers</li>
                <li>🔔 Status indicators and badges</li>
                <li>🎭 Smooth animations and transitions</li>
              </ul>
            </div>
            
            <div style={styles.summaryCard}>
              <h3 style={styles.summaryCardTitle}>🛡️ Security & Reliability</h3>
              <ul style={styles.summaryList}>
                <li>🔒 Email verification status</li>
                <li>🆔 User ID display (truncated)</li>
                <li>📅 Account creation tracking</li>
                <li>🔐 Provider authentication info</li>
                <li>⚠️ Comprehensive error handling</li>
                <li>🧪 Fallback for missing data</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div style={styles.footer}>
        <div style={styles.footerContent}>
          <h3 style={styles.footerTitle}>🚀 Ready to Implement?</h3>
          <p style={styles.footerText}>
            Choose the approach that best fits your project needs. Both implementations provide 
            comprehensive user authentication with enhanced profile display and real-time state tracking.
          </p>
          <div style={styles.footerLinks}>
            <div style={styles.footerLink}>
              <strong>useState Version:</strong> <code>src/components/EnhancedGoogleSignIn.jsx</code>
            </div>
            <div style={styles.footerLink}>
              <strong>react-firebase-hooks:</strong> <code>src/components/ReactFirebaseHooksSignIn.jsx</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    lineHeight: '1.6',
    color: '#333',
    minHeight: '100vh'
  },
  navigation: {
    backgroundColor: '#1e293b',
    color: 'white',
    padding: '24px 20px',
    position: 'sticky',
    top: 0,
    zIndex: 1000,
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
  },
  navContent: {
    maxWidth: '1200px',
    margin: '0 auto',
    textAlign: 'center'
  },
  navTitle: {
    fontSize: '32px',
    fontWeight: '700',
    margin: '0 0 8px 0'
  },
  navSubtitle: {
    fontSize: '16px',
    margin: '0 0 24px 0',
    opacity: '0.8'
  },
  demoSelector: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  demoButton: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '12px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    color: 'white',
    border: '2px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  activeDemoButton: {
    backgroundColor: 'white',
    color: '#1e293b',
    borderColor: 'white'
  },
  demoIcon: {
    fontSize: '16px'
  },
  demoButtonText: {
    fontSize: '14px'
  },
  infoBar: {
    backgroundColor: '#f8fafc',
    padding: '20px',
    borderBottom: '1px solid #e2e8f0'
  },
  infoContent: {
    maxWidth: '1200px',
    margin: '0 auto',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: '32px',
    flexWrap: 'wrap'
  },
  currentDemo: {
    display: 'flex',
    alignItems: 'center',
    gap: '16px'
  },
  currentDemoIcon: {
    fontSize: '32px'
  },
  currentDemoText: {
    flex: 1
  },
  currentDemoTitle: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 4px 0'
  },
  currentDemoDescription: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0'
  },
  features: {
    flex: 1,
    minWidth: '300px'
  },
  featuresTitle: {
    fontSize: '14px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  featuresTags: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '8px'
  },
  featureTag: {
    fontSize: '12px',
    padding: '4px 8px',
    backgroundColor: '#dbeafe',
    color: '#1e40af',
    borderRadius: '12px',
    fontWeight: '500'
  },
  demoArea: {
    minHeight: '80vh'
  },
  summarySection: {
    backgroundColor: 'white',
    padding: '60px 20px',
    borderTop: '1px solid #e2e8f0'
  },
  summaryContent: {
    maxWidth: '1200px',
    margin: '0 auto'
  },
  summaryTitle: {
    fontSize: '32px',
    fontWeight: '600',
    textAlign: 'center',
    color: '#1e293b',
    margin: '0 0 40px 0'
  },
  summaryGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gap: '24px'
  },
  summaryCard: {
    backgroundColor: '#f8fafc',
    borderRadius: '12px',
    padding: '24px',
    border: '1px solid #e2e8f0'
  },
  summaryCardTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  summaryList: {
    margin: '0',
    padding: '0 0 0 16px',
    color: '#64748b'
  },
  footer: {
    backgroundColor: '#1e293b',
    color: 'white',
    padding: '40px 20px'
  },
  footerContent: {
    maxWidth: '1200px',
    margin: '0 auto',
    textAlign: 'center'
  },
  footerTitle: {
    fontSize: '24px',
    fontWeight: '600',
    margin: '0 0 16px 0'
  },
  footerText: {
    fontSize: '16px',
    margin: '0 0 24px 0',
    opacity: '0.8',
    lineHeight: '1.6'
  },
  footerLinks: {
    display: 'flex',
    justifyContent: 'center',
    gap: '32px',
    flexWrap: 'wrap'
  },
  footerLink: {
    fontSize: '14px',
    opacity: '0.8'
  }
};

export default EnhancedAuthDemo;
