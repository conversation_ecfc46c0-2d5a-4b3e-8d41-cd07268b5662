import React, { useState, useEffect } from 'react';
import { signInWithPopup, signOut, onAuthStateChanged } from 'firebase/auth';
import { auth, googleProvider } from '../firebase';

const GoogleSignIn = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [signingIn, setSigningIn] = useState(false);
  const [signingOut, setSigningOut] = useState(false);
  const [error, setError] = useState(null);

  // Listen for authentication state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  // Handle Google Sign-In
  const handleSignIn = async () => {
    setSigningIn(true);
    setError(null);
    
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;
      
      console.log('User signed in successfully:', {
        name: user.displayName,
        email: user.email,
        photoURL: user.photoURL,
        uid: user.uid
      });
      
      // User state will be updated automatically by onAuthStateChanged
    } catch (error) {
      console.error('Error signing in with Google:', error);
      setError(error.message);
    } finally {
      setSigningIn(false);
    }
  };

  // Handle Sign Out with loading state
  const handleSignOut = async () => {
    setSigningOut(true);
    setError(null);

    try {
      await signOut(auth);
      console.log('User signed out successfully');
      // User state will be updated automatically by onAuthStateChanged
    } catch (error) {
      console.error('Error signing out:', error);
      setError(error.message);
    } finally {
      setSigningOut(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div style={styles.container}>
        <div style={styles.loadingSpinner}>
          <div style={styles.spinner}></div>
          <p style={styles.loadingText}>Loading...</p>
        </div>
      </div>
    );
  }

  // User is signed in - show profile
  if (user) {
    return (
      <div style={styles.container}>
        <div style={styles.profileCard}>
          <h2 style={styles.welcomeTitle}>Welcome!</h2>
          
          <div style={styles.profileInfo}>
            <img 
              src={user.photoURL || '/default-avatar.png'} 
              alt={user.displayName || 'User'}
              style={styles.profileImage}
              onError={(e) => {
                e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA1MEMzNy44NSA1MCAyOCA0MC4xNSAyOCAyOEMyOCAxNS44NSAzNy44NSA2IDUwIDZDNjIuMTUgNiA3MiAxNS44NSA3MiAyOEM3MiA0MC4xNSA2Mi4xNSA1MCA1MCA1MFpNNTAgNTZDNjYuNjcgNTYgODAgNjIuMzMgODAgNzBWNzZINzJWNzBDNzIgNjYuNjcgNjMuMzMgNjAgNTAgNjBDMzYuNjcgNjAgMjggNjYuNjcgMjggNzBWNzZIMjBWNzBDMjAgNjIuMzMgMzMuMzMgNTYgNTAgNTZaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo=';
              }}
            />
            
            <div style={styles.userDetails}>
              <h3 style={styles.userName}>
                {user.displayName || 'Anonymous User'}
              </h3>
              <p style={styles.userEmail}>
                {user.email}
              </p>
              <p style={styles.userInfo}>
                <strong>User ID:</strong> {user.uid}
              </p>
              <p style={styles.userInfo}>
                <strong>Last Sign In:</strong> {new Date(user.metadata.lastSignInTime).toLocaleString()}
              </p>
            </div>
          </div>

          {error && (
            <div style={styles.errorMessage}>
              <p>Error: {error}</p>
            </div>
          )}

          <div style={styles.logoutSection}>
            <button
              onClick={handleSignOut}
              disabled={signingOut}
              style={{
                ...styles.signOutButton,
                opacity: signingOut ? 0.6 : 1,
                cursor: signingOut ? 'not-allowed' : 'pointer'
              }}
            >
              {signingOut ? '🔄 Signing out...' : '🚪 Sign Out'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User is not signed in - show sign in button
  return (
    <div style={styles.container}>
      <div style={styles.signInCard}>
        <h2 style={styles.title}>Google Sign-In Demo</h2>
        <p style={styles.description}>
          Click the button below to sign in with your Google account
        </p>

        {error && (
          <div style={styles.errorMessage}>
            <p>Error: {error}</p>
          </div>
        )}

        <button 
          onClick={handleSignIn}
          disabled={signingIn}
          style={{
            ...styles.signInButton,
            opacity: signingIn ? 0.6 : 1,
            cursor: signingIn ? 'not-allowed' : 'pointer'
          }}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" style={styles.googleIcon}>
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          {signingIn ? 'Signing in...' : 'Sign in with Google'}
        </button>
      </div>
    </div>
  );
};

// Styles object
const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f5f5f5',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  signInCard: {
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '40px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '400px',
    width: '100%'
  },
  profileCard: {
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '40px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '500px',
    width: '100%'
  },
  title: {
    fontSize: '28px',
    fontWeight: '600',
    color: '#333',
    marginBottom: '16px'
  },
  welcomeTitle: {
    fontSize: '32px',
    fontWeight: '600',
    color: '#333',
    marginBottom: '24px'
  },
  description: {
    fontSize: '16px',
    color: '#666',
    marginBottom: '32px',
    lineHeight: '1.5'
  },
  signInButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '12px',
    padding: '14px 28px',
    fontSize: '16px',
    fontWeight: '500',
    backgroundColor: '#4285f4',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    transition: 'all 0.2s ease',
    width: '100%',
    maxWidth: '280px',
    margin: '0 auto'
  },
  logoutSection: {
    marginTop: '24px',
    textAlign: 'center'
  },
  signOutButton: {
    padding: '12px 24px',
    fontSize: '16px',
    fontWeight: '500',
    backgroundColor: '#dc3545',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    minWidth: '150px'
  },
  googleIcon: {
    flexShrink: 0
  },
  profileInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '24px',
    marginBottom: '24px',
    textAlign: 'left'
  },
  profileImage: {
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    objectFit: 'cover',
    border: '3px solid #e0e0e0'
  },
  userDetails: {
    flex: 1
  },
  userName: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#333',
    margin: '0 0 8px 0'
  },
  userEmail: {
    fontSize: '16px',
    color: '#666',
    margin: '0 0 12px 0'
  },
  userInfo: {
    fontSize: '14px',
    color: '#888',
    margin: '4px 0'
  },
  errorMessage: {
    backgroundColor: '#fee',
    border: '1px solid #fcc',
    borderRadius: '6px',
    padding: '12px',
    margin: '16px 0',
    color: '#c33'
  },
  loadingSpinner: {
    textAlign: 'center'
  },
  spinner: {
    width: '40px',
    height: '40px',
    border: '4px solid #f3f3f3',
    borderTop: '4px solid #4285f4',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 16px'
  },
  loadingText: {
    fontSize: '16px',
    color: '#666'
  }
};

export default GoogleSignIn;
