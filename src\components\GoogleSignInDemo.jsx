import React from 'react';
import GoogleSignIn from './GoogleSignIn';

const GoogleSignInDemo = () => {
  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <h1 style={styles.title}>Google Sign-In Component Demo</h1>
        <p style={styles.subtitle}>
          A complete functional React component with Firebase Google Authentication
        </p>
      </div>
      
      <div style={styles.demoSection}>
        <GoogleSignIn />
      </div>
      
      <div style={styles.features}>
        <h2 style={styles.featuresTitle}>Features Included:</h2>
        <ul style={styles.featuresList}>
          <li>✅ Google Sign-In with popup authentication</li>
          <li>✅ Real-time authentication state management</li>
          <li>✅ User profile display with name and photo</li>
          <li>✅ Automatic fallback for missing profile images</li>
          <li>✅ Loading states and error handling</li>
          <li>✅ Sign out functionality</li>
          <li>✅ Responsive design</li>
          <li>✅ Clean, modern UI</li>
        </ul>
      </div>
      
      <div style={styles.codeInfo}>
        <h3 style={styles.codeTitle}>Component Location:</h3>
        <code style={styles.codePath}>src/components/GoogleSignIn.jsx</code>
        <p style={styles.codeDescription}>
          This is a self-contained functional React component that demonstrates 
          complete Google authentication flow using Firebase v9 modular SDK.
        </p>
      </div>
    </div>
  );
};

const styles = {
  container: {
    minHeight: '100vh',
    backgroundColor: '#f8f9fa',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  header: {
    textAlign: 'center',
    padding: '40px 20px 20px',
    backgroundColor: 'white',
    borderBottom: '1px solid #e9ecef'
  },
  title: {
    fontSize: '36px',
    fontWeight: '700',
    color: '#212529',
    margin: '0 0 12px 0'
  },
  subtitle: {
    fontSize: '18px',
    color: '#6c757d',
    margin: '0',
    maxWidth: '600px',
    marginLeft: 'auto',
    marginRight: 'auto'
  },
  demoSection: {
    padding: '0',
    backgroundColor: '#f5f5f5'
  },
  features: {
    maxWidth: '800px',
    margin: '0 auto',
    padding: '40px 20px',
    backgroundColor: 'white'
  },
  featuresTitle: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#212529',
    marginBottom: '20px',
    textAlign: 'center'
  },
  featuresList: {
    listStyle: 'none',
    padding: '0',
    margin: '0',
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: '12px'
  },
  codeInfo: {
    maxWidth: '800px',
    margin: '0 auto',
    padding: '40px 20px',
    backgroundColor: '#f8f9fa',
    textAlign: 'center',
    borderTop: '1px solid #e9ecef'
  },
  codeTitle: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#212529',
    marginBottom: '12px'
  },
  codePath: {
    backgroundColor: '#e9ecef',
    padding: '8px 16px',
    borderRadius: '6px',
    fontSize: '16px',
    fontFamily: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
    color: '#495057',
    display: 'inline-block',
    marginBottom: '16px'
  },
  codeDescription: {
    fontSize: '16px',
    color: '#6c757d',
    lineHeight: '1.6',
    margin: '0'
  }
};

export default GoogleSignInDemo;
