import React, { useState } from 'react';
import { AuthProvider } from '../contexts/AuthContext';
import ProtectedRoute from './ProtectedRoute';
import ProtectedRouteFirebase from './ProtectedRouteFirebase';
import ProtectedRouteHooks from './ProtectedRouteHooks';
import ProtectedContent from './ProtectedContent';
import GoogleSignIn from './GoogleSignIn';

const ProtectedRouteDemo = () => {
  const [currentDemo, setCurrentDemo] = useState('auth-context');

  const demos = {
    'auth-context': {
      title: 'AuthContext Version',
      description: 'Uses existing AuthContext for authentication state',
      component: () => (
        <AuthProvider>
          <ProtectedRoute>
            <ProtectedContent />
          </ProtectedRoute>
        </AuthProvider>
      ),
      features: [
        '✅ Uses existing AuthContext',
        '✅ Consistent with app architecture',
        '✅ Shared authentication state',
        '✅ Custom fallback components',
        '✅ Email verification support'
      ]
    },
    'firebase-direct': {
      title: 'Firebase Direct',
      description: 'Works directly with Firebase auth without context',
      component: () => (
        <ProtectedRouteFirebase>
          <ProtectedContent />
        </ProtectedRouteFirebase>
      ),
      features: [
        '✅ No context dependency',
        '✅ Direct Firebase integration',
        '✅ Enhanced error handling',
        '✅ Callback support',
        '✅ Standalone implementation'
      ]
    },
    'react-hooks': {
      title: 'react-firebase-hooks',
      description: 'Uses react-firebase-hooks for simplified state management',
      component: () => (
        <ProtectedRouteHooks>
          <ProtectedContent />
        </ProtectedRouteHooks>
      ),
      features: [
        '✅ Built-in loading states',
        '✅ Automatic error handling',
        '✅ TypeScript support',
        '✅ Minimal boilerplate',
        '✅ Well-tested library'
      ]
    },
    'with-fallback': {
      title: 'Custom Fallback',
      description: 'ProtectedRoute with custom fallback component',
      component: () => (
        <AuthProvider>
          <ProtectedRoute fallback={CustomLoginPage}>
            <ProtectedContent />
          </ProtectedRoute>
        </AuthProvider>
      ),
      features: [
        '✅ Custom fallback component',
        '✅ Branded login experience',
        '✅ Seamless integration',
        '✅ Custom styling',
        '✅ Enhanced UX'
      ]
    },
    'email-verification': {
      title: 'Email Verification Required',
      description: 'ProtectedRoute that requires email verification',
      component: () => (
        <AuthProvider>
          <ProtectedRoute requireEmailVerification={true}>
            <ProtectedContent />
          </ProtectedRoute>
        </AuthProvider>
      ),
      features: [
        '✅ Email verification check',
        '✅ Security enhancement',
        '✅ Verification status display',
        '✅ User guidance',
        '✅ Account security'
      ]
    }
  };

  const CurrentComponent = demos[currentDemo].component;
  const currentDemoData = demos[currentDemo];

  return (
    <div style={styles.container}>
      {/* Header */}
      <div style={styles.header}>
        <h1 style={styles.title}>🔒 ProtectedRoute Component Demo</h1>
        <p style={styles.subtitle}>
          Comprehensive authentication protection for React routes using Firebase
        </p>
        
        {/* Demo Selector */}
        <div style={styles.demoSelector}>
          {Object.entries(demos).map(([key, { title }]) => (
            <button
              key={key}
              onClick={() => setCurrentDemo(key)}
              style={{
                ...styles.demoButton,
                ...(currentDemo === key ? styles.activeDemoButton : {})
              }}
            >
              {title}
            </button>
          ))}
        </div>
      </div>

      {/* Current Demo Info */}
      <div style={styles.infoSection}>
        <div style={styles.infoCard}>
          <h2 style={styles.infoTitle}>{currentDemoData.title}</h2>
          <p style={styles.infoDescription}>{currentDemoData.description}</p>
          
          <div style={styles.featuresSection}>
            <h3 style={styles.featuresTitle}>🎯 Features:</h3>
            <div style={styles.featuresList}>
              {currentDemoData.features.map((feature, index) => (
                <div key={index} style={styles.featureItem}>
                  {feature}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Live Demo */}
      <div style={styles.demoArea}>
        <CurrentComponent />
      </div>
    </div>
  );
};

// Custom login page component for fallback demo
const CustomLoginPage = () => {
  return (
    <div style={customLoginStyles.container}>
      <div style={customLoginStyles.card}>
        <div style={customLoginStyles.logo}>🔐</div>
        <h2 style={customLoginStyles.title}>Custom Login Page</h2>
        <p style={customLoginStyles.description}>
          This is a custom fallback component rendered when the user is not authenticated.
        </p>
        <div style={customLoginStyles.loginSection}>
          <GoogleSignIn />
        </div>
        <div style={customLoginStyles.info}>
          <p style={customLoginStyles.infoText}>
            💡 This demonstrates how to use a custom fallback component with ProtectedRoute
          </p>
        </div>
      </div>
    </div>
  );
};

const customLoginStyles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#1e293b',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  card: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '48px',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
    maxWidth: '500px',
    width: '100%'
  },
  logo: {
    fontSize: '48px',
    marginBottom: '24px'
  },
  title: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  description: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 32px 0',
    lineHeight: '1.6'
  },
  loginSection: {
    marginBottom: '32px'
  },
  info: {
    padding: '20px',
    backgroundColor: '#f1f5f9',
    borderRadius: '12px',
    border: '1px solid #e2e8f0'
  },
  infoText: {
    fontSize: '14px',
    color: '#475569',
    margin: '0',
    fontStyle: 'italic'
  }
};

const styles = {
  container: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    lineHeight: '1.6',
    color: '#333',
    backgroundColor: '#f8fafc'
  },
  header: {
    textAlign: 'center',
    padding: '40px 20px',
    background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
    color: 'white'
  },
  title: {
    fontSize: '36px',
    fontWeight: '700',
    margin: '0 0 12px 0'
  },
  subtitle: {
    fontSize: '18px',
    fontWeight: '300',
    margin: '0 0 32px 0',
    opacity: '0.9'
  },
  demoSelector: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  demoButton: {
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    color: 'white',
    border: '2px solid rgba(255, 255, 255, 0.3)',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  activeDemoButton: {
    backgroundColor: 'white',
    color: '#3b82f6',
    borderColor: 'white'
  },
  infoSection: {
    padding: '40px 20px',
    backgroundColor: 'white'
  },
  infoCard: {
    maxWidth: '800px',
    margin: '0 auto',
    textAlign: 'center'
  },
  infoTitle: {
    fontSize: '28px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  infoDescription: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 24px 0'
  },
  featuresSection: {
    textAlign: 'left',
    backgroundColor: '#f8fafc',
    padding: '24px',
    borderRadius: '12px',
    border: '1px solid #e2e8f0'
  },
  featuresTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  featuresList: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: '8px'
  },
  featureItem: {
    fontSize: '14px',
    color: '#64748b'
  },
  demoArea: {
    backgroundColor: '#f1f5f9'
  }
};

export default ProtectedRouteDemo;
