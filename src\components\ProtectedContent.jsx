import React from 'react';
import { useAuth } from '../contexts/AuthContext';

/**
 * Example protected content component
 * This represents content that should only be visible to authenticated users
 */
const ProtectedContent = () => {
  const { user } = useAuth();

  return (
    <div style={styles.container}>
      <div style={styles.content}>
        <div style={styles.header}>
          <h1 style={styles.title}>🔒 Protected Content</h1>
          <div style={styles.badge}>
            <span style={styles.badgeIcon}>✅</span>
            <span style={styles.badgeText}>Access Granted</span>
          </div>
        </div>

        <div style={styles.welcomeSection}>
          <div style={styles.userInfo}>
            <img 
              src={user?.photoURL || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zMCAzMEMyMi43MjUgMzAgMTYuOCAyNC4wNzUgMTYuOCAxNi44QzE2LjggOS41MjUgMjIuNzI1IDMuNiAzMCAzLjZDMzcuMjc1IDMuNiA0My4yIDkuNTI1IDQzLjIgMTYuOEM0My4yIDI0LjA3NSAzNy4yNzUgMzAgMzAgMzBaTTMwIDMzLjZDNDAgMzMuNiA0OCAzNy40IDQ4IDQyVjQ1LjZINDJWNDJDNDIgNDAgMzggMzYgMzAgMzZDMjIgMzYgMTggNDAgMTggNDJWNDUuNkgxMlY0MkMxMiAzNy40IDIwIDMzLjYgMzAgMzMuNloiIGZpbGw9IiM5QjlCQTAiLz4KPC9zdmc+Cg=='} 
              alt={user?.displayName || 'User'}
              style={styles.avatar}
            />
            <div style={styles.userDetails}>
              <h2 style={styles.welcomeTitle}>
                Welcome, {user?.displayName?.split(' ')[0] || 'User'}! 👋
              </h2>
              <p style={styles.userEmail}>{user?.email}</p>
            </div>
          </div>
        </div>

        <div style={styles.contentGrid}>
          <div style={styles.contentCard}>
            <div style={styles.cardIcon}>📊</div>
            <h3 style={styles.cardTitle}>Dashboard</h3>
            <p style={styles.cardDescription}>
              Access your personal dashboard with analytics and insights.
            </p>
            <div style={styles.cardStats}>
              <div style={styles.stat}>
                <span style={styles.statValue}>24</span>
                <span style={styles.statLabel}>Projects</span>
              </div>
              <div style={styles.stat}>
                <span style={styles.statValue}>156</span>
                <span style={styles.statLabel}>Tasks</span>
              </div>
            </div>
          </div>

          <div style={styles.contentCard}>
            <div style={styles.cardIcon}>💼</div>
            <h3 style={styles.cardTitle}>Workspace</h3>
            <p style={styles.cardDescription}>
              Collaborate with your team and manage shared resources.
            </p>
            <div style={styles.cardActions}>
              <button style={styles.actionButton}>Open Workspace</button>
            </div>
          </div>

          <div style={styles.contentCard}>
            <div style={styles.cardIcon}>⚙️</div>
            <h3 style={styles.cardTitle}>Settings</h3>
            <p style={styles.cardDescription}>
              Customize your account preferences and security settings.
            </p>
            <div style={styles.cardActions}>
              <button style={styles.actionButton}>Manage Settings</button>
            </div>
          </div>

          <div style={styles.contentCard}>
            <div style={styles.cardIcon}>📈</div>
            <h3 style={styles.cardTitle}>Analytics</h3>
            <p style={styles.cardDescription}>
              View detailed reports and performance metrics.
            </p>
            <div style={styles.cardStats}>
              <div style={styles.stat}>
                <span style={styles.statValue}>89%</span>
                <span style={styles.statLabel}>Completion</span>
              </div>
              <div style={styles.stat}>
                <span style={styles.statValue}>+12%</span>
                <span style={styles.statLabel}>Growth</span>
              </div>
            </div>
          </div>
        </div>

        <div style={styles.securityInfo}>
          <h3 style={styles.securityTitle}>🔐 Security Information</h3>
          <div style={styles.securityGrid}>
            <div style={styles.securityItem}>
              <span style={styles.securityLabel}>Authentication Status:</span>
              <span style={styles.securityValue}>✅ Authenticated</span>
            </div>
            <div style={styles.securityItem}>
              <span style={styles.securityLabel}>Email Verification:</span>
              <span style={{
                ...styles.securityValue,
                color: user?.emailVerified ? '#10b981' : '#f59e0b'
              }}>
                {user?.emailVerified ? '✅ Verified' : '⚠️ Pending'}
              </span>
            </div>
            <div style={styles.securityItem}>
              <span style={styles.securityLabel}>Last Sign In:</span>
              <span style={styles.securityValue}>
                {user?.metadata?.lastSignInTime ? 
                  new Date(user.metadata.lastSignInTime).toLocaleString() : 
                  'Unknown'
                }
              </span>
            </div>
            <div style={styles.securityItem}>
              <span style={styles.securityLabel}>Account Created:</span>
              <span style={styles.securityValue}>
                {user?.metadata?.creationTime ? 
                  new Date(user.metadata.creationTime).toLocaleDateString() : 
                  'Unknown'
                }
              </span>
            </div>
          </div>
        </div>

        <div style={styles.footer}>
          <p style={styles.footerText}>
            🎉 Congratulations! You have successfully accessed protected content. 
            This page is only visible to authenticated users.
          </p>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    minHeight: '100vh',
    backgroundColor: '#f8fafc',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  content: {
    maxWidth: '1200px',
    margin: '0 auto'
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '32px',
    padding: '24px',
    backgroundColor: 'white',
    borderRadius: '12px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e2e8f0'
  },
  title: {
    fontSize: '32px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0'
  },
  badge: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    backgroundColor: '#dcfce7',
    borderRadius: '20px',
    border: '1px solid #bbf7d0'
  },
  badgeIcon: {
    fontSize: '16px'
  },
  badgeText: {
    fontSize: '14px',
    fontWeight: '600',
    color: '#166534'
  },
  welcomeSection: {
    marginBottom: '32px',
    padding: '32px',
    backgroundColor: 'white',
    borderRadius: '12px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e2e8f0'
  },
  userInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '20px'
  },
  avatar: {
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    border: '4px solid #e2e8f0',
    objectFit: 'cover'
  },
  userDetails: {
    flex: 1
  },
  welcomeTitle: {
    fontSize: '28px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 8px 0'
  },
  userEmail: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0'
  },
  contentGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: '24px',
    marginBottom: '32px'
  },
  contentCard: {
    padding: '24px',
    backgroundColor: 'white',
    borderRadius: '12px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e2e8f0',
    transition: 'transform 0.2s ease, box-shadow 0.2s ease'
  },
  cardIcon: {
    fontSize: '32px',
    marginBottom: '16px'
  },
  cardTitle: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  cardDescription: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0 0 20px 0',
    lineHeight: '1.5'
  },
  cardStats: {
    display: 'flex',
    gap: '20px'
  },
  stat: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  },
  statValue: {
    fontSize: '24px',
    fontWeight: '700',
    color: '#3b82f6'
  },
  statLabel: {
    fontSize: '12px',
    color: '#64748b',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  },
  cardActions: {
    display: 'flex',
    justifyContent: 'center'
  },
  actionButton: {
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#3b82f6',
    color: 'white',
    border: 'none',
    borderRadius: '6px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  securityInfo: {
    marginBottom: '32px',
    padding: '24px',
    backgroundColor: 'white',
    borderRadius: '12px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e2e8f0'
  },
  securityTitle: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 20px 0'
  },
  securityGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: '16px'
  },
  securityItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px',
    backgroundColor: '#f8fafc',
    borderRadius: '6px',
    border: '1px solid #e2e8f0'
  },
  securityLabel: {
    fontSize: '14px',
    fontWeight: '500',
    color: '#475569'
  },
  securityValue: {
    fontSize: '14px',
    fontWeight: '600',
    color: '#10b981'
  },
  footer: {
    padding: '24px',
    backgroundColor: '#f0fdf4',
    borderRadius: '12px',
    border: '1px solid #bbf7d0',
    textAlign: 'center'
  },
  footerText: {
    fontSize: '16px',
    color: '#166534',
    margin: '0',
    lineHeight: '1.6'
  }
};

export default ProtectedContent;
