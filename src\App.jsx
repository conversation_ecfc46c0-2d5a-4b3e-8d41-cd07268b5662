import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import LoginButton from './components/LoginButton';
import UserProfile from './components/UserProfile';
import './App.css';

function AuthenticatedApp() {
  const { user, loading, isAuthenticated } = useAuth();

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px'
      }}>
        Loading...
      </div>
    );
  }

  return (
    <div style={{
      maxWidth: '600px',
      margin: '0 auto',
      padding: '40px 20px',
      textAlign: 'center'
    }}>
      <h1>React Firebase Google Authentication</h1>

      {isAuthenticated ? (
        <div>
          <h2>Welcome back!</h2>
          <UserProfile />
          <div style={{ marginTop: '20px' }}>
            <p>You are successfully signed in with Google!</p>
          </div>
        </div>
      ) : (
        <div>
          <h2>Please sign in to continue</h2>
          <p style={{ marginBottom: '20px' }}>
            Click the button below to sign in with your Google account
          </p>
          <LoginButton />
        </div>
      )}
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <AuthenticatedApp />
    </AuthProvider>
  );
}

export default App;
