import React, { useState } from 'react';
import { AuthProvider } from '../contexts/AuthContext';
import ProtectedRoute from '../components/ProtectedRoute';
import ProtectedRouteFirebase from '../components/ProtectedRouteFirebase';
import ProtectedRouteHooks from '../components/ProtectedRouteHooks';

/**
 * Collection of ProtectedRoute usage examples
 */

// Example 1: Basic usage with AuthContext
export const BasicProtectedRoute = () => {
  return (
    <AuthProvider>
      <ProtectedRoute>
        <div>
          <h1>Protected Content</h1>
          <p>This content is only visible to authenticated users.</p>
        </div>
      </ProtectedRoute>
    </AuthProvider>
  );
};

// Example 2: ProtectedRoute with custom fallback component
const CustomLoginComponent = () => (
  <div style={{ textAlign: 'center', padding: '50px' }}>
    <h2>🔒 Please Sign In</h2>
    <p>You need to be authenticated to access this content.</p>
    <button onClick={() => window.location.href = '/login'}>
      Go to Login
    </button>
  </div>
);

export const ProtectedRouteWithFallback = () => {
  return (
    <AuthProvider>
      <ProtectedRoute fallback={CustomLoginComponent}>
        <div>
          <h1>Dashboard</h1>
          <p>Welcome to your dashboard!</p>
        </div>
      </ProtectedRoute>
    </AuthProvider>
  );
};

// Example 3: ProtectedRoute with redirect
export const ProtectedRouteWithRedirect = () => {
  return (
    <AuthProvider>
      <ProtectedRoute redirectTo="/login">
        <div>
          <h1>Admin Panel</h1>
          <p>Administrative content here.</p>
        </div>
      </ProtectedRoute>
    </AuthProvider>
  );
};

// Example 4: ProtectedRoute requiring email verification
export const ProtectedRouteWithEmailVerification = () => {
  return (
    <AuthProvider>
      <ProtectedRoute requireEmailVerification={true}>
        <div>
          <h1>Verified Users Only</h1>
          <p>This content requires email verification.</p>
        </div>
      </ProtectedRoute>
    </AuthProvider>
  );
};

// Example 5: Firebase direct implementation
export const FirebaseDirectProtectedRoute = () => {
  const handleAuthStateChange = (user) => {
    console.log('Auth state changed:', user ? 'Signed in' : 'Signed out');
  };

  return (
    <ProtectedRouteFirebase 
      onAuthStateChange={handleAuthStateChange}
      requireEmailVerification={false}
    >
      <div>
        <h1>Firebase Direct</h1>
        <p>This uses Firebase auth directly without context.</p>
      </div>
    </ProtectedRouteFirebase>
  );
};

// Example 6: react-firebase-hooks implementation
export const ReactFirebaseHooksProtectedRoute = () => {
  return (
    <ProtectedRouteHooks>
      <div>
        <h1>react-firebase-hooks</h1>
        <p>This uses react-firebase-hooks for state management.</p>
      </div>
    </ProtectedRouteHooks>
  );
};

// Example 7: Nested protected routes
export const NestedProtectedRoutes = () => {
  return (
    <AuthProvider>
      <ProtectedRoute>
        <div>
          <h1>Main Dashboard</h1>
          <p>This is the main protected area.</p>
          
          {/* Nested protection with email verification */}
          <ProtectedRoute requireEmailVerification={true}>
            <div style={{ 
              padding: '20px', 
              backgroundColor: '#f0f0f0', 
              margin: '20px 0',
              borderRadius: '8px'
            }}>
              <h2>Verified Section</h2>
              <p>This nested section requires email verification.</p>
            </div>
          </ProtectedRoute>
        </div>
      </ProtectedRoute>
    </AuthProvider>
  );
};

// Example 8: Multiple protection levels
export const MultiLevelProtection = () => {
  return (
    <AuthProvider>
      <div>
        <h1>Multi-Level Protection Demo</h1>
        
        {/* Level 1: Basic authentication */}
        <ProtectedRoute>
          <div style={{ padding: '20px', border: '2px solid #3b82f6', margin: '10px 0' }}>
            <h2>🔒 Level 1: Authenticated Users</h2>
            <p>Basic authentication required.</p>
            
            {/* Level 2: Email verification required */}
            <ProtectedRoute requireEmailVerification={true}>
              <div style={{ padding: '15px', border: '2px solid #f59e0b', margin: '10px 0' }}>
                <h3>📧 Level 2: Verified Email</h3>
                <p>Email verification required.</p>
                
                {/* Level 3: Custom additional check */}
                <ProtectedRoute 
                  fallback={() => (
                    <div style={{ padding: '10px', backgroundColor: '#fef2f2', borderRadius: '4px' }}>
                      <p>🚫 Level 3: Additional verification needed</p>
                    </div>
                  )}
                >
                  <div style={{ padding: '10px', border: '2px solid #10b981', margin: '10px 0' }}>
                    <h4>✅ Level 3: Fully Verified</h4>
                    <p>Maximum security level achieved!</p>
                  </div>
                </ProtectedRoute>
              </div>
            </ProtectedRoute>
          </div>
        </ProtectedRoute>
      </div>
    </AuthProvider>
  );
};

// Example 9: Conditional protection
export const ConditionalProtection = ({ requireAuth = true }) => {
  const content = (
    <div>
      <h1>Conditionally Protected Content</h1>
      <p>This content may or may not be protected based on props.</p>
    </div>
  );

  if (requireAuth) {
    return (
      <AuthProvider>
        <ProtectedRoute>
          {content}
        </ProtectedRoute>
      </AuthProvider>
    );
  }

  return content;
};

// Example 10: ProtectedRoute with loading customization
export const CustomLoadingProtectedRoute = () => {
  return (
    <AuthProvider>
      <ProtectedRoute>
        <div>
          <h1>Custom Loading Example</h1>
          <p>This demonstrates the default loading behavior.</p>
          <p>You can customize loading states by modifying the ProtectedRoute component.</p>
        </div>
      </ProtectedRoute>
    </AuthProvider>
  );
};

// Example usage in a React Router setup
export const RouterExample = () => {
  // This would typically be used with React Router
  return (
    <div>
      <h2>React Router Integration Example</h2>
      <pre style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '20px', 
        borderRadius: '8px',
        overflow: 'auto'
      }}>
{`import { BrowserRouter, Routes, Route } from 'react-router-dom';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route 
            path="/dashboard" 
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin" 
            element={
              <ProtectedRoute requireEmailVerification={true}>
                <AdminPanel />
              </ProtectedRoute>
            } 
          />
        </Routes>
      </BrowserRouter>
    </AuthProvider>
  );
}`}
      </pre>
    </div>
  );
};

// Main examples showcase component
const ProtectedRouteExamples = () => {
  const [currentExample, setCurrentExample] = useState('basic');

  const examples = {
    basic: { component: BasicProtectedRoute, title: 'Basic Usage' },
    fallback: { component: ProtectedRouteWithFallback, title: 'Custom Fallback' },
    redirect: { component: ProtectedRouteWithRedirect, title: 'With Redirect' },
    verification: { component: ProtectedRouteWithEmailVerification, title: 'Email Verification' },
    firebase: { component: FirebaseDirectProtectedRoute, title: 'Firebase Direct' },
    hooks: { component: ReactFirebaseHooksProtectedRoute, title: 'react-firebase-hooks' },
    nested: { component: NestedProtectedRoutes, title: 'Nested Routes' },
    multilevel: { component: MultiLevelProtection, title: 'Multi-Level Protection' },
    conditional: { component: () => <ConditionalProtection requireAuth={true} />, title: 'Conditional' },
    router: { component: RouterExample, title: 'Router Integration' }
  };

  const CurrentExample = examples[currentExample].component;

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px' }}>
      <h1>ProtectedRoute Examples</h1>
      
      <div style={{ marginBottom: '20px' }}>
        {Object.entries(examples).map(([key, { title }]) => (
          <button
            key={key}
            onClick={() => setCurrentExample(key)}
            style={{
              margin: '5px',
              padding: '10px 15px',
              backgroundColor: currentExample === key ? '#3b82f6' : '#f3f4f6',
              color: currentExample === key ? 'white' : '#374151',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            {title}
          </button>
        ))}
      </div>

      <div style={{ 
        border: '1px solid #e5e7eb', 
        borderRadius: '8px', 
        padding: '20px',
        backgroundColor: 'white'
      }}>
        <CurrentExample />
      </div>
    </div>
  );
};

export default ProtectedRouteExamples;
