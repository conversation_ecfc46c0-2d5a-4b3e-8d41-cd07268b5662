import React, { useState } from 'react';
import GoogleSignIn from '../components/GoogleSignIn';

// Example 1: Basic usage
export const BasicGoogleSignIn = () => {
  return (
    <div>
      <h2>Basic Google Sign-In</h2>
      <GoogleSignIn />
    </div>
  );
};

// Example 2: With custom styling container
export const StyledGoogleSignIn = () => {
  return (
    <div style={{
      backgroundColor: '#1a1a1a',
      minHeight: '100vh',
      padding: '20px'
    }}>
      <h2 style={{ color: 'white', textAlign: 'center' }}>
        Dark Theme Example
      </h2>
      <GoogleSignIn />
    </div>
  );
};

// Example 3: With callback handling
export const GoogleSignInWithCallbacks = () => {
  const [authEvents, setAuthEvents] = useState([]);

  const addEvent = (event) => {
    setAuthEvents(prev => [...prev, {
      timestamp: new Date().toLocaleTimeString(),
      event
    }]);
  };

  // You can extend the GoogleSignIn component to accept callbacks
  // This is just an example of how you might handle events
  React.useEffect(() => {
    // Listen for auth state changes globally
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        addEvent(`User signed in: ${user.displayName}`);
      } else {
        addEvent('User signed out');
      }
    });

    return unsubscribe;
  }, []);

  return (
    <div style={{ display: 'flex', gap: '20px', padding: '20px' }}>
      <div style={{ flex: 1 }}>
        <GoogleSignIn />
      </div>
      
      <div style={{ 
        flex: 1, 
        backgroundColor: '#f8f9fa', 
        padding: '20px', 
        borderRadius: '8px',
        maxHeight: '400px',
        overflow: 'auto'
      }}>
        <h3>Authentication Events:</h3>
        {authEvents.length === 0 ? (
          <p>No events yet...</p>
        ) : (
          <ul style={{ listStyle: 'none', padding: 0 }}>
            {authEvents.map((event, index) => (
              <li key={index} style={{
                padding: '8px',
                marginBottom: '4px',
                backgroundColor: 'white',
                borderRadius: '4px',
                fontSize: '14px'
              }}>
                <strong>{event.timestamp}:</strong> {event.event}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

// Example 4: Multiple instances (for testing)
export const MultipleGoogleSignIn = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h2>Multiple Instances (Shared State)</h2>
      <p>
        Both components below share the same Firebase auth state, 
        so signing in on one will update both.
      </p>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr', 
        gap: '20px',
        marginTop: '20px'
      }}>
        <div style={{ border: '2px solid #007bff', borderRadius: '8px' }}>
          <h3 style={{ textAlign: 'center', color: '#007bff' }}>Instance 1</h3>
          <GoogleSignIn />
        </div>
        
        <div style={{ border: '2px solid #28a745', borderRadius: '8px' }}>
          <h3 style={{ textAlign: 'center', color: '#28a745' }}>Instance 2</h3>
          <GoogleSignIn />
        </div>
      </div>
    </div>
  );
};

// Main demo component that shows all examples
const GoogleSignInUsageExamples = () => {
  const [currentExample, setCurrentExample] = useState('basic');

  const examples = {
    basic: { component: BasicGoogleSignIn, title: 'Basic Usage' },
    styled: { component: StyledGoogleSignIn, title: 'Custom Styling' },
    callbacks: { component: GoogleSignInWithCallbacks, title: 'With Event Tracking' },
    multiple: { component: MultipleGoogleSignIn, title: 'Multiple Instances' }
  };

  const CurrentComponent = examples[currentExample].component;

  return (
    <div>
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderBottom: '1px solid #ddd',
        textAlign: 'center'
      }}>
        <h1>GoogleSignIn Component Usage Examples</h1>
        
        <div style={{ marginTop: '20px' }}>
          {Object.entries(examples).map(([key, { title }]) => (
            <button
              key={key}
              onClick={() => setCurrentExample(key)}
              style={{
                margin: '0 8px',
                padding: '8px 16px',
                backgroundColor: currentExample === key ? '#007bff' : '#f8f9fa',
                color: currentExample === key ? 'white' : '#333',
                border: '1px solid #ddd',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {title}
            </button>
          ))}
        </div>
      </div>
      
      <CurrentComponent />
    </div>
  );
};

export default GoogleSignInUsageExamples;
