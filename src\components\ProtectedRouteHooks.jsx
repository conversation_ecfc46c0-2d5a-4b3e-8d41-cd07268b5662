import React from 'react';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '../firebase';

/**
 * ProtectedRoute component using react-firebase-hooks
 * Simplest implementation with built-in loading and error states
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authenticated
 * @param {React.ComponentType} props.fallback - Component to render if not authenticated (optional)
 * @param {string} props.redirectTo - Path to redirect to if not authenticated (optional)
 * @param {boolean} props.requireEmailVerification - Whether to require email verification (optional)
 * @param {Function} props.onAuthStateChange - Callback when auth state changes (optional)
 */
const ProtectedRouteHooks = ({ 
  children, 
  fallback: Fallback,
  redirectTo,
  requireEmailVerification = false,
  onAuthStateChange
}) => {
  const [user, loading, error] = useAuthState(auth);

  // Call optional callback when auth state changes
  React.useEffect(() => {
    if (onAuthStateChange) {
      onAuthStateChange(user);
    }
  }, [user, onAuthStateChange]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div style={styles.loadingContainer}>
        <div style={styles.loadingCard}>
          <div style={styles.hooksBadge}>
            <span style={styles.hooksIcon}>⚛️</span>
            react-firebase-hooks
          </div>
          <div style={styles.spinner}></div>
          <h3 style={styles.loadingTitle}>Checking Authentication</h3>
          <p style={styles.loadingText}>Using react-firebase-hooks for state management...</p>
        </div>
      </div>
    );
  }

  // Show error state if there's an auth error
  if (error) {
    return (
      <div style={styles.errorContainer}>
        <div style={styles.errorCard}>
          <div style={styles.hooksBadge}>
            <span style={styles.hooksIcon}>⚛️</span>
            react-firebase-hooks
          </div>
          <h2 style={styles.errorTitle}>⚠️ Authentication Error</h2>
          <p style={styles.errorMessage}>
            react-firebase-hooks detected an authentication error.
          </p>
          <div style={styles.errorDetails}>
            <p style={styles.errorCode}>Error Code: {error.code}</p>
            <p style={styles.errorText}>{error.message}</p>
          </div>
          <div style={styles.errorActions}>
            <button 
              onClick={() => window.location.reload()}
              style={styles.retryButton}
            >
              🔄 Retry Authentication
            </button>
            <button 
              onClick={() => window.location.href = '/'}
              style={styles.homeButton}
            >
              🏠 Go Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!user) {
    // If a custom fallback component is provided, render it
    if (Fallback) {
      return <Fallback />;
    }

    // If redirectTo is specified, redirect to that path
    if (redirectTo) {
      window.location.href = redirectTo;
      return (
        <div style={styles.redirectingContainer}>
          <div style={styles.redirectingCard}>
            <div style={styles.hooksBadge}>
              <span style={styles.hooksIcon}>⚛️</span>
              react-firebase-hooks
            </div>
            <div style={styles.redirectingSpinner}></div>
            <p style={styles.redirectingText}>Redirecting to login...</p>
          </div>
        </div>
      );
    }

    // Default: render login prompt
    return (
      <div style={styles.unauthorizedContainer}>
        <div style={styles.unauthorizedCard}>
          <div style={styles.hooksBadge}>
            <span style={styles.hooksIcon}>⚛️</span>
            react-firebase-hooks
          </div>
          <div style={styles.lockIcon}>🔒</div>
          <h2 style={styles.unauthorizedTitle}>Protected Route</h2>
          <p style={styles.unauthorizedMessage}>
            This route is protected and requires authentication.
          </p>
          <div style={styles.authInfo}>
            <div style={styles.infoItem}>
              <span style={styles.infoIcon}>🔍</span>
              <span style={styles.infoText}>Authentication state checked with react-firebase-hooks</span>
            </div>
            <div style={styles.infoItem}>
              <span style={styles.infoIcon}>🚫</span>
              <span style={styles.infoText}>No authenticated user found</span>
            </div>
          </div>
          <div style={styles.actionButtons}>
            <button 
              onClick={() => window.location.href = '/'}
              style={styles.primaryButton}
            >
              🏠 Go to Login
            </button>
            <button 
              onClick={() => window.location.reload()}
              style={styles.secondaryButton}
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check email verification if required
  if (requireEmailVerification && !user.emailVerified) {
    return (
      <div style={styles.verificationContainer}>
        <div style={styles.verificationCard}>
          <div style={styles.hooksBadge}>
            <span style={styles.hooksIcon}>⚛️</span>
            react-firebase-hooks
          </div>
          <div style={styles.emailIcon}>📧</div>
          <h2 style={styles.verificationTitle}>Email Verification Required</h2>
          <p style={styles.verificationMessage}>
            Your email must be verified to access this protected content.
          </p>
          
          <div style={styles.userCard}>
            <img 
              src={user.photoURL || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEMxNS4xNSAyMCAxMS4yIDE2LjA1IDExLjIgMTEuMkMxMS4yIDYuMzUgMTUuMTUgMi40IDIwIDIuNEMyNC44NSAyLjQgMjguOCA2LjM1IDI4LjggMTEuMkMyOC44IDE2LjA1IDI0Ljg1IDIwIDIwIDIwWk0yMCAyMi40QzI2LjY3IDIyLjQgMzIgMjQuOTMgMzIgMjhWMzAuNEgyOFYyOEMyOCAyNi42NyAyNS4zMyAyNCAyMCAyNEMxNC42NyAyNCAxMiAyNi42NyAxMiAyOFYzMC40SDhWMjhDOCAyNC45MyAxMy4zMyAyMi40IDIwIDIyLjRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo='} 
              alt={user.displayName || 'User'}
              style={styles.userAvatar}
            />
            <div style={styles.userInfo}>
              <h3 style={styles.userName}>{user.displayName || 'User'}</h3>
              <p style={styles.userEmail}>{user.email}</p>
              <div style={styles.verificationBadge}>
                <span style={styles.badgeIcon}>❌</span>
                <span style={styles.badgeText}>Email Not Verified</span>
              </div>
            </div>
          </div>

          <div style={styles.verificationActions}>
            <button 
              onClick={() => window.location.reload()}
              style={styles.checkButton}
            >
              ✅ Check Verification Status
            </button>
            <button 
              onClick={() => window.history.back()}
              style={styles.backButton}
            >
              ← Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User is authenticated (and email verified if required) - render children
  return <>{children}</>;
};

// Styles for the ProtectedRouteHooks component
const styles = {
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#0f172a',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  loadingCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '48px',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
    maxWidth: '400px',
    border: '1px solid #e2e8f0'
  },
  hooksBadge: {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '6px',
    backgroundColor: '#dbeafe',
    color: '#1e40af',
    padding: '6px 12px',
    borderRadius: '20px',
    fontSize: '12px',
    fontWeight: '600',
    marginBottom: '20px',
    border: '1px solid #bfdbfe'
  },
  hooksIcon: {
    fontSize: '14px'
  },
  spinner: {
    width: '48px',
    height: '48px',
    border: '4px solid #f1f5f9',
    borderTop: '4px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 24px'
  },
  loadingTitle: {
    fontSize: '20px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  loadingText: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0'
  },
  errorContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#0f172a',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  errorCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '48px',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
    maxWidth: '500px',
    width: '100%',
    border: '1px solid #fecaca'
  },
  errorTitle: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#dc2626',
    margin: '0 0 16px 0'
  },
  errorMessage: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 24px 0'
  },
  errorDetails: {
    padding: '20px',
    backgroundColor: '#fef2f2',
    borderRadius: '12px',
    marginBottom: '24px',
    textAlign: 'left'
  },
  errorCode: {
    fontSize: '14px',
    fontWeight: '600',
    color: '#7f1d1d',
    margin: '0 0 8px 0'
  },
  errorText: {
    fontSize: '14px',
    color: '#7f1d1d',
    margin: '0',
    fontFamily: 'monospace'
  },
  errorActions: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  retryButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#dc2626',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  homeButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#6b7280',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  redirectingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#0f172a',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  redirectingCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '40px',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
    border: '1px solid #e2e8f0'
  },
  redirectingSpinner: {
    width: '32px',
    height: '32px',
    border: '3px solid #f1f5f9',
    borderTop: '3px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '20px auto 16px'
  },
  redirectingText: {
    fontSize: '16px',
    color: '#64748b'
  },
  unauthorizedContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#0f172a',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  unauthorizedCard: {
    backgroundColor: 'white',
    borderRadius: '24px',
    padding: '48px',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
    maxWidth: '500px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  lockIcon: {
    fontSize: '48px',
    marginBottom: '20px'
  },
  unauthorizedTitle: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  unauthorizedMessage: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 32px 0',
    lineHeight: '1.6'
  },
  authInfo: {
    padding: '24px',
    backgroundColor: '#f8fafc',
    borderRadius: '12px',
    marginBottom: '32px',
    border: '1px solid #e2e8f0'
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    marginBottom: '12px'
  },
  infoIcon: {
    fontSize: '16px'
  },
  infoText: {
    fontSize: '14px',
    color: '#475569',
    textAlign: 'left'
  },
  actionButtons: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  primaryButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#3b82f6',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  secondaryButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#6b7280',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  verificationContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#0f172a',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  verificationCard: {
    backgroundColor: 'white',
    borderRadius: '24px',
    padding: '48px',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
    maxWidth: '550px',
    width: '100%',
    border: '1px solid #fed7aa'
  },
  emailIcon: {
    fontSize: '48px',
    marginBottom: '20px'
  },
  verificationTitle: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#f59e0b',
    margin: '0 0 16px 0'
  },
  verificationMessage: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 32px 0',
    lineHeight: '1.6'
  },
  userCard: {
    display: 'flex',
    alignItems: 'center',
    gap: '20px',
    padding: '24px',
    backgroundColor: '#fffbeb',
    borderRadius: '12px',
    border: '1px solid #fed7aa',
    marginBottom: '32px',
    textAlign: 'left'
  },
  userAvatar: {
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    border: '3px solid #fed7aa'
  },
  userInfo: {
    flex: 1
  },
  userName: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#92400e',
    margin: '0 0 4px 0'
  },
  userEmail: {
    fontSize: '14px',
    color: '#b45309',
    margin: '0 0 12px 0'
  },
  verificationBadge: {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '6px',
    padding: '4px 8px',
    backgroundColor: '#fef2f2',
    borderRadius: '12px',
    border: '1px solid #fecaca'
  },
  badgeIcon: {
    fontSize: '12px'
  },
  badgeText: {
    fontSize: '12px',
    fontWeight: '500',
    color: '#dc2626'
  },
  verificationActions: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  checkButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#f59e0b',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  backButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#6b7280',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  }
};

export default ProtectedRouteHooks;
