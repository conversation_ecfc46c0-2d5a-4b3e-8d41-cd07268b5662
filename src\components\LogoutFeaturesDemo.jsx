import React, { useState } from 'react';
import GoogleSignIn from './GoogleSignIn';
import EnhancedGoogleSignIn from './EnhancedGoogleSignIn';
import ReactFirebaseHooksSignIn from './ReactFirebaseHooksSignIn';
import EnhancedLogoutComponent from './EnhancedLogoutComponent';

const LogoutFeaturesDemo = () => {
  const [currentDemo, setCurrentDemo] = useState('enhanced-logout');

  const demos = {
    'enhanced-logout': {
      component: EnhancedLogoutComponent,
      title: 'Enhanced Logout Features',
      description: 'Confirmation dialogs, loading states, and multiple logout options',
      features: [
        '✅ Logout confirmation dialog',
        '⚡ Quick logout option',
        '🔄 Loading states during sign out',
        '📊 Session tracking',
        '⚠️ Error handling',
        '🎨 Modern UI design'
      ]
    },
    'original': {
      component: GoogleSignIn,
      title: 'Enhanced Original Component',
      description: 'Original component with improved logout button and loading states',
      features: [
        '✅ Enhanced logout button with loading state',
        '🔄 Visual feedback during sign out',
        '⚠️ Error handling',
        '🎯 Simple and clean design'
      ]
    },
    'enhanced-useState': {
      component: EnhancedGoogleSignIn,
      title: 'useState with Logout',
      description: 'Manual state management with comprehensive logout functionality',
      features: [
        '✅ Multiple logout button styles',
        '🔄 Loading states',
        '📊 Session statistics',
        '⚠️ Error handling',
        '🎨 Modern design'
      ]
    },
    'react-hooks': {
      component: ReactFirebaseHooksSignIn,
      title: 'react-firebase-hooks with Logout',
      description: 'Simplified state management with built-in logout functionality',
      features: [
        '✅ Built-in useSignOut hook',
        '🔄 Automatic loading states',
        '📊 Session dashboard',
        '⚠️ Built-in error handling',
        '🎨 Dark theme design'
      ]
    }
  };

  const CurrentComponent = demos[currentDemo].component;
  const currentDemoData = demos[currentDemo];

  return (
    <div style={styles.container}>
      {/* Header */}
      <div style={styles.header}>
        <h1 style={styles.title}>🚪 Firebase Logout Functionality Demo</h1>
        <p style={styles.subtitle}>
          Comprehensive logout implementations using Firebase auth signOut() method
        </p>
        
        {/* Demo Selector */}
        <div style={styles.demoSelector}>
          {Object.entries(demos).map(([key, { title }]) => (
            <button
              key={key}
              onClick={() => setCurrentDemo(key)}
              style={{
                ...styles.demoButton,
                ...(currentDemo === key ? styles.activeDemoButton : {})
              }}
            >
              {title}
            </button>
          ))}
        </div>
      </div>

      {/* Current Demo Info */}
      <div style={styles.infoSection}>
        <div style={styles.infoCard}>
          <h2 style={styles.infoTitle}>{currentDemoData.title}</h2>
          <p style={styles.infoDescription}>{currentDemoData.description}</p>
          
          <div style={styles.featuresSection}>
            <h3 style={styles.featuresTitle}>🎯 Features:</h3>
            <div style={styles.featuresList}>
              {currentDemoData.features.map((feature, index) => (
                <div key={index} style={styles.featureItem}>
                  {feature}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Live Demo */}
      <div style={styles.demoArea}>
        <CurrentComponent />
      </div>

      {/* Implementation Guide */}
      <div style={styles.implementationSection}>
        <div style={styles.implementationContent}>
          <h2 style={styles.implementationTitle}>🛠️ Logout Implementation Guide</h2>
          
          <div style={styles.implementationGrid}>
            <div style={styles.implementationCard}>
              <h3 style={styles.cardTitle}>1. Basic Logout</h3>
              <div style={styles.codeBlock}>
                <pre style={styles.code}>
{`import { signOut } from 'firebase/auth';
import { auth } from '../firebase';

const handleSignOut = async () => {
  try {
    await signOut(auth);
    console.log('User signed out');
  } catch (error) {
    console.error('Error:', error);
  }
};`}
                </pre>
              </div>
            </div>
            
            <div style={styles.implementationCard}>
              <h3 style={styles.cardTitle}>2. Logout with Loading State</h3>
              <div style={styles.codeBlock}>
                <pre style={styles.code}>
{`const [signingOut, setSigningOut] = useState(false);

const handleSignOut = async () => {
  setSigningOut(true);
  try {
    await signOut(auth);
  } catch (error) {
    setError(error.message);
  } finally {
    setSigningOut(false);
  }
};`}
                </pre>
              </div>
            </div>
            
            <div style={styles.implementationCard}>
              <h3 style={styles.cardTitle}>3. Logout with Confirmation</h3>
              <div style={styles.codeBlock}>
                <pre style={styles.code}>
{`const [showConfirm, setShowConfirm] = useState(false);

const handleLogoutClick = () => {
  setShowConfirm(true);
};

const confirmLogout = async () => {
  setSigningOut(true);
  try {
    await signOut(auth);
    setShowConfirm(false);
  } catch (error) {
    setError(error.message);
  } finally {
    setSigningOut(false);
  }
};`}
                </pre>
              </div>
            </div>
            
            <div style={styles.implementationCard}>
              <h3 style={styles.cardTitle}>4. react-firebase-hooks</h3>
              <div style={styles.codeBlock}>
                <pre style={styles.code}>
{`import { useSignOut } from 'react-firebase-hooks/auth';

const [signOut, loading, error] = useSignOut(auth);

const handleSignOut = async () => {
  try {
    await signOut();
  } catch (err) {
    console.error('Error:', err);
  }
};`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Best Practices */}
      <div style={styles.bestPracticesSection}>
        <div style={styles.bestPracticesContent}>
          <h2 style={styles.bestPracticesTitle}>✨ Logout Best Practices</h2>
          
          <div style={styles.practicesGrid}>
            <div style={styles.practiceCard}>
              <h3 style={styles.practiceTitle}>🔄 Loading States</h3>
              <ul style={styles.practiceList}>
                <li>Show loading indicator during sign out</li>
                <li>Disable logout button while processing</li>
                <li>Provide visual feedback to users</li>
                <li>Prevent multiple simultaneous logout attempts</li>
              </ul>
            </div>
            
            <div style={styles.practiceCard}>
              <h3 style={styles.practiceTitle}>⚠️ Error Handling</h3>
              <ul style={styles.practiceList}>
                <li>Catch and display logout errors</li>
                <li>Provide user-friendly error messages</li>
                <li>Log errors for debugging</li>
                <li>Allow retry on failure</li>
              </ul>
            </div>
            
            <div style={styles.practiceCard}>
              <h3 style={styles.practiceTitle}>🎯 User Experience</h3>
              <ul style={styles.practiceList}>
                <li>Clear logout button placement</li>
                <li>Confirmation for important actions</li>
                <li>Quick logout for convenience</li>
                <li>Session information display</li>
              </ul>
            </div>
            
            <div style={styles.practiceCard}>
              <h3 style={styles.practiceTitle}>🔒 Security</h3>
              <ul style={styles.practiceList}>
                <li>Clear all user data on logout</li>
                <li>Redirect to safe page after logout</li>
                <li>Invalidate session tokens</li>
                <li>Clear sensitive information from memory</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div style={styles.footer}>
        <div style={styles.footerContent}>
          <h3 style={styles.footerTitle}>🎯 Choose Your Implementation</h3>
          <p style={styles.footerText}>
            All implementations use Firebase's <code>signOut()</code> method with different UX approaches.
            Choose based on your application's needs and user experience requirements.
          </p>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    lineHeight: '1.6',
    color: '#333',
    backgroundColor: '#f8fafc'
  },
  header: {
    textAlign: 'center',
    padding: '40px 20px',
    background: 'linear-gradient(135deg, #dc2626 0%, #991b1b 100%)',
    color: 'white'
  },
  title: {
    fontSize: '36px',
    fontWeight: '700',
    margin: '0 0 12px 0'
  },
  subtitle: {
    fontSize: '18px',
    fontWeight: '300',
    margin: '0 0 32px 0',
    opacity: '0.9'
  },
  demoSelector: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  demoButton: {
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    color: 'white',
    border: '2px solid rgba(255, 255, 255, 0.3)',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  activeDemoButton: {
    backgroundColor: 'white',
    color: '#dc2626',
    borderColor: 'white'
  },
  infoSection: {
    padding: '40px 20px',
    backgroundColor: 'white'
  },
  infoCard: {
    maxWidth: '800px',
    margin: '0 auto',
    textAlign: 'center'
  },
  infoTitle: {
    fontSize: '28px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 12px 0'
  },
  infoDescription: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 24px 0'
  },
  featuresSection: {
    textAlign: 'left',
    backgroundColor: '#f8fafc',
    padding: '24px',
    borderRadius: '12px',
    border: '1px solid #e2e8f0'
  },
  featuresTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  featuresList: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: '8px'
  },
  featureItem: {
    fontSize: '14px',
    color: '#64748b'
  },
  demoArea: {
    backgroundColor: '#f1f5f9'
  },
  implementationSection: {
    padding: '60px 20px',
    backgroundColor: 'white'
  },
  implementationContent: {
    maxWidth: '1200px',
    margin: '0 auto'
  },
  implementationTitle: {
    fontSize: '32px',
    fontWeight: '600',
    textAlign: 'center',
    color: '#1e293b',
    margin: '0 0 40px 0'
  },
  implementationGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: '24px'
  },
  implementationCard: {
    backgroundColor: '#f8fafc',
    borderRadius: '12px',
    padding: '24px',
    border: '1px solid #e2e8f0'
  },
  cardTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  codeBlock: {
    backgroundColor: '#1e293b',
    borderRadius: '8px',
    overflow: 'hidden'
  },
  code: {
    color: '#e2e8f0',
    fontSize: '12px',
    padding: '16px',
    margin: '0',
    overflow: 'auto',
    lineHeight: '1.4'
  },
  bestPracticesSection: {
    padding: '60px 20px',
    backgroundColor: '#f8fafc'
  },
  bestPracticesContent: {
    maxWidth: '1200px',
    margin: '0 auto'
  },
  bestPracticesTitle: {
    fontSize: '32px',
    fontWeight: '600',
    textAlign: 'center',
    color: '#1e293b',
    margin: '0 0 40px 0'
  },
  practicesGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gap: '24px'
  },
  practiceCard: {
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '24px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e2e8f0'
  },
  practiceTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  practiceList: {
    margin: '0',
    padding: '0 0 0 16px',
    color: '#64748b'
  },
  footer: {
    backgroundColor: '#1e293b',
    color: 'white',
    padding: '40px 20px'
  },
  footerContent: {
    maxWidth: '800px',
    margin: '0 auto',
    textAlign: 'center'
  },
  footerTitle: {
    fontSize: '24px',
    fontWeight: '600',
    margin: '0 0 16px 0'
  },
  footerText: {
    fontSize: '16px',
    margin: '0',
    opacity: '0.8',
    lineHeight: '1.6'
  }
};

export default LogoutFeaturesDemo;
