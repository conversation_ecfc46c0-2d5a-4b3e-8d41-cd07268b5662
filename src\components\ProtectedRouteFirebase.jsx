import React, { useState, useEffect } from 'react';
import { onAuthStateChanged } from 'firebase/auth';
import { auth } from '../firebase';

/**
 * ProtectedRoute component that works directly with Firebase auth
 * Does not require AuthContext - uses Firebase auth directly
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authenticated
 * @param {React.ComponentType} props.fallback - Component to render if not authenticated (optional)
 * @param {string} props.redirectTo - Path to redirect to if not authenticated (optional)
 * @param {boolean} props.requireEmailVerification - Whether to require email verification (optional)
 * @param {Function} props.onAuthStateChange - Callback when auth state changes (optional)
 */
const ProtectedRouteFirebase = ({ 
  children, 
  fallback: Fallback,
  redirectTo,
  requireEmailVerification = false,
  onAuthStateChange
}) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(
      auth, 
      (currentUser) => {
        setUser(currentUser);
        setLoading(false);
        setAuthError(null);
        
        // Call optional callback
        if (onAuthStateChange) {
          onAuthStateChange(currentUser);
        }
        
        console.log('Auth state changed:', currentUser ? 'Signed in' : 'Signed out');
      },
      (error) => {
        console.error('Auth state change error:', error);
        setAuthError(error);
        setLoading(false);
      }
    );

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [onAuthStateChange]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div style={styles.loadingContainer}>
        <div style={styles.loadingSpinner}>
          <div style={styles.spinner}></div>
          <p style={styles.loadingText}>Verifying authentication...</p>
          <p style={styles.loadingSubtext}>Please wait while we check your login status</p>
        </div>
      </div>
    );
  }

  // Show error state if there's an auth error
  if (authError) {
    return (
      <div style={styles.errorContainer}>
        <div style={styles.errorCard}>
          <h2 style={styles.errorTitle}>⚠️ Authentication Error</h2>
          <p style={styles.errorMessage}>
            There was an error checking your authentication status.
          </p>
          <div style={styles.errorDetails}>
            <p style={styles.errorText}>{authError.message}</p>
          </div>
          <button 
            onClick={() => window.location.reload()}
            style={styles.retryButton}
          >
            🔄 Retry
          </button>
        </div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!user) {
    // If a custom fallback component is provided, render it
    if (Fallback) {
      return <Fallback />;
    }

    // If redirectTo is specified, redirect to that path
    if (redirectTo) {
      window.location.href = redirectTo;
      return (
        <div style={styles.redirectingContainer}>
          <div style={styles.redirectingCard}>
            <div style={styles.redirectingSpinner}></div>
            <p style={styles.redirectingText}>Redirecting to login...</p>
          </div>
        </div>
      );
    }

    // Default: render login prompt
    return (
      <div style={styles.unauthorizedContainer}>
        <div style={styles.unauthorizedCard}>
          <div style={styles.lockIcon}>🔒</div>
          <h2 style={styles.unauthorizedTitle}>Authentication Required</h2>
          <p style={styles.unauthorizedMessage}>
            You must be signed in to access this protected content.
          </p>
          <div style={styles.authPrompt}>
            <p style={styles.promptText}>
              Please sign in with your account to continue
            </p>
            <div style={styles.actionButtons}>
              <button 
                onClick={() => window.location.href = '/'}
                style={styles.homeButton}
              >
                🏠 Go to Home
              </button>
              <button 
                onClick={() => window.location.reload()}
                style={styles.refreshButton}
              >
                🔄 Refresh
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Check email verification if required
  if (requireEmailVerification && !user.emailVerified) {
    return (
      <div style={styles.verificationContainer}>
        <div style={styles.verificationCard}>
          <div style={styles.emailIcon}>📧</div>
          <h2 style={styles.verificationTitle}>Email Verification Required</h2>
          <p style={styles.verificationMessage}>
            Your email address needs to be verified before you can access this content.
          </p>
          <div style={styles.userInfo}>
            <div style={styles.userDetails}>
              <img 
                src={user.photoURL || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEMxNS4xNSAyMCAxMS4yIDE2LjA1IDExLjIgMTEuMkMxMS4yIDYuMzUgMTUuMTUgMi40IDIwIDIuNEMyNC44NSAyLjQgMjguOCA2LjM1IDI4LjggMTEuMkMyOC44IDE2LjA1IDI0Ljg1IDIwIDIwIDIwWk0yMCAyMi40QzI2LjY3IDIyLjQgMzIgMjQuOTMgMzIgMjhWMzAuNEgyOFYyOEMyOCAyNi42NyAyNS4zMyAyNCAyMCAyNEMxNC42NyAyNCAxMiAyNi42NyAxMiAyOFYzMC40SDhWMjhDOCAyNC45MyAxMy4zMyAyMi40IDIwIDIyLjRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo='} 
                alt={user.displayName || 'User'}
                style={styles.userAvatar}
              />
              <div style={styles.userText}>
                <p style={styles.userName}>{user.displayName || 'User'}</p>
                <p style={styles.userEmail}>{user.email}</p>
              </div>
            </div>
            <div style={styles.verificationStatus}>
              <span style={styles.statusLabel}>Status:</span>
              <span style={styles.unverifiedBadge}>❌ Not Verified</span>
            </div>
          </div>
          <div style={styles.verificationActions}>
            <button 
              onClick={() => window.location.reload()}
              style={styles.checkVerificationButton}
            >
              ✅ Check Verification Status
            </button>
            <button 
              onClick={() => window.history.back()}
              style={styles.backButton}
            >
              ← Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User is authenticated (and email verified if required) - render children
  return <>{children}</>;
};

// Styles for the ProtectedRouteFirebase component
const styles = {
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f1f5f9',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  loadingSpinner: {
    textAlign: 'center',
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '48px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e2e8f0',
    maxWidth: '400px'
  },
  spinner: {
    width: '48px',
    height: '48px',
    border: '4px solid #f1f5f9',
    borderTop: '4px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 20px'
  },
  loadingText: {
    fontSize: '18px',
    fontWeight: '600',
    color: '#1e293b',
    margin: '0 0 8px 0'
  },
  loadingSubtext: {
    fontSize: '14px',
    color: '#64748b',
    margin: '0'
  },
  errorContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f1f5f9',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  errorCard: {
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '40px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '450px',
    width: '100%',
    border: '1px solid #fecaca'
  },
  errorTitle: {
    fontSize: '24px',
    fontWeight: '600',
    color: '#dc2626',
    margin: '0 0 16px 0'
  },
  errorMessage: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 20px 0'
  },
  errorDetails: {
    padding: '16px',
    backgroundColor: '#fef2f2',
    borderRadius: '8px',
    marginBottom: '24px'
  },
  errorText: {
    fontSize: '14px',
    color: '#7f1d1d',
    margin: '0',
    fontFamily: 'monospace'
  },
  retryButton: {
    padding: '12px 24px',
    fontSize: '16px',
    fontWeight: '500',
    backgroundColor: '#dc2626',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  redirectingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f1f5f9',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  },
  redirectingCard: {
    backgroundColor: 'white',
    borderRadius: '16px',
    padding: '40px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    border: '1px solid #e2e8f0'
  },
  redirectingSpinner: {
    width: '32px',
    height: '32px',
    border: '3px solid #f1f5f9',
    borderTop: '3px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 16px'
  },
  redirectingText: {
    fontSize: '16px',
    color: '#64748b'
  },
  unauthorizedContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f1f5f9',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  unauthorizedCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '48px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '500px',
    width: '100%',
    border: '1px solid #e2e8f0'
  },
  lockIcon: {
    fontSize: '48px',
    marginBottom: '20px'
  },
  unauthorizedTitle: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0 0 16px 0'
  },
  unauthorizedMessage: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 32px 0',
    lineHeight: '1.6'
  },
  authPrompt: {
    padding: '24px',
    backgroundColor: '#f8fafc',
    borderRadius: '12px',
    border: '1px solid #e2e8f0'
  },
  promptText: {
    fontSize: '14px',
    color: '#475569',
    margin: '0 0 20px 0'
  },
  actionButtons: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  homeButton: {
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#3b82f6',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  refreshButton: {
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#6b7280',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  verificationContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f1f5f9',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    padding: '20px'
  },
  verificationCard: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '48px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '550px',
    width: '100%',
    border: '1px solid #fed7aa'
  },
  emailIcon: {
    fontSize: '48px',
    marginBottom: '20px'
  },
  verificationTitle: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#f59e0b',
    margin: '0 0 16px 0'
  },
  verificationMessage: {
    fontSize: '16px',
    color: '#64748b',
    margin: '0 0 32px 0',
    lineHeight: '1.6'
  },
  userInfo: {
    padding: '24px',
    backgroundColor: '#fffbeb',
    borderRadius: '12px',
    border: '1px solid #fed7aa',
    marginBottom: '32px'
  },
  userDetails: {
    display: 'flex',
    alignItems: 'center',
    gap: '16px',
    marginBottom: '16px'
  },
  userAvatar: {
    width: '48px',
    height: '48px',
    borderRadius: '50%',
    border: '2px solid #fed7aa'
  },
  userText: {
    textAlign: 'left',
    flex: 1
  },
  userName: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#92400e',
    margin: '0 0 4px 0'
  },
  userEmail: {
    fontSize: '14px',
    color: '#b45309',
    margin: '0'
  },
  verificationStatus: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  statusLabel: {
    fontSize: '14px',
    fontWeight: '500',
    color: '#92400e'
  },
  unverifiedBadge: {
    fontSize: '14px',
    fontWeight: '600',
    color: '#dc2626'
  },
  verificationActions: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  },
  checkVerificationButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#f59e0b',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  },
  backButton: {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: '500',
    backgroundColor: '#6b7280',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  }
};

export default ProtectedRouteFirebase;
