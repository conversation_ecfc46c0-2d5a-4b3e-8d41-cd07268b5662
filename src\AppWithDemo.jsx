import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import LoginButton from './components/LoginButton';
import UserProfile from './components/UserProfile';
import GoogleSignInDemo from './components/GoogleSignInDemo';
// Import the AuthenticatedApp component from App.jsx
const AuthenticatedApp = () => {
  const { user, loading, isAuthenticated } = useAuth();

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px'
      }}>
        Loading...
      </div>
    );
  }

  return (
    <div style={{
      maxWidth: '600px',
      margin: '0 auto',
      padding: '40px 20px',
      textAlign: 'center'
    }}>
      <h1>React Firebase Google Authentication</h1>

      {isAuthenticated ? (
        <div>
          <h2>Welcome back!</h2>
          <UserProfile />
          <div style={{ marginTop: '20px' }}>
            <p>You are successfully signed in with Google!</p>
          </div>
        </div>
      ) : (
        <div>
          <h2>Please sign in to continue</h2>
          <p style={{ marginBottom: '20px' }}>
            Click the button below to sign in with your Google account
          </p>
          <LoginButton />
        </div>
      )}
    </div>
  );
};
import './App.css';

function AppWithDemo() {
  const [currentView, setCurrentView] = useState('demo');

  const views = {
    demo: { component: GoogleSignInDemo, title: 'New GoogleSignIn Component' },
    original: { component: () => <AuthProvider><AuthenticatedApp /></AuthProvider>, title: 'Original Implementation' }
  };

  const CurrentComponent = views[currentView].component;

  return (
    <div>
      {/* Navigation */}
      <div style={{
        backgroundColor: '#343a40',
        padding: '16px',
        textAlign: 'center',
        position: 'sticky',
        top: 0,
        zIndex: 1000
      }}>
        <h1 style={{ 
          color: 'white', 
          margin: '0 0 16px 0',
          fontSize: '24px'
        }}>
          React Firebase Google Authentication Demo
        </h1>
        
        <div>
          {Object.entries(views).map(([key, { title }]) => (
            <button
              key={key}
              onClick={() => setCurrentView(key)}
              style={{
                margin: '0 8px',
                padding: '10px 20px',
                backgroundColor: currentView === key ? '#007bff' : '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                transition: 'background-color 0.2s ease'
              }}
            >
              {title}
            </button>
          ))}
        </div>
      </div>
      
      {/* Current View */}
      <CurrentComponent />
      
      {/* Footer */}
      <div style={{
        backgroundColor: '#f8f9fa',
        padding: '20px',
        textAlign: 'center',
        borderTop: '1px solid #dee2e6',
        color: '#6c757d',
        fontSize: '14px'
      }}>
        <p>
          <strong>Current View:</strong> {views[currentView].title}
        </p>
        <p>
          Switch between the new self-contained GoogleSignIn component and the original 
          implementation using AuthContext and separate components.
        </p>
      </div>
    </div>
  );
}

export default AppWithDemo;
