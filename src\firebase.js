// Firebase v9 modular SDK initialization
import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';

// Your web app's Firebase configuration
// Replace these placeholder values with your actual Firebase project configuration
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id",
  measurementId: "your-measurement-id" // Optional for Google Analytics
};

// Initialize Firebase app
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Google Auth Provider
export const googleProvider = new GoogleAuthProvider();

// Configure Google Auth Provider with additional scopes (optional)
googleProvider.addScope('profile');
googleProvider.addScope('email');

// Set custom parameters for the OAuth flow (optional)
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// Export the Firebase app instance (optional, for other Firebase services)
export default app;
