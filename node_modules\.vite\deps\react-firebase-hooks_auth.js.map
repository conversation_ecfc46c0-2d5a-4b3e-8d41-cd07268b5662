{"version": 3, "sources": ["../../react-firebase-hooks/auth/dist/index.esm.js"], "sourcesContent": ["import { onAuthStateChanged, sendEmailVerification, createUserWithEmailAndPassword, sendPasswordResetEmail, sendSignInLinkToEmail, signInWithEmailAndPassword, signInWithEmailLink, FacebookAuthProvider, <PERSON>ith<PERSON><PERSON>uthProvider, GoogleAuthProvider, <PERSON>AuthProvider, OAuthProvider, signInWithPopup, updateEmail, updatePassword, updateProfile, verifyBeforeUpdateEmail, onIdTokenChanged } from 'firebase/auth';\nimport { useMemo, useReducer, useCallback, useEffect, useState } from 'react';\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar defaultState = function (defaultValue) {\r\n    return {\r\n        loading: defaultValue === undefined || defaultValue === null,\r\n        value: defaultValue,\r\n    };\r\n};\r\nvar reducer = function () { return function (state, action) {\r\n    switch (action.type) {\r\n        case 'error':\r\n            return __assign(__assign({}, state), { error: action.error, loading: false, value: undefined });\r\n        case 'reset':\r\n            return defaultState(action.defaultValue);\r\n        case 'value':\r\n            return __assign(__assign({}, state), { error: undefined, loading: false, value: action.value });\r\n        default:\r\n            return state;\r\n    }\r\n}; };\r\nvar useLoadingValue = (function (getDefaultValue) {\r\n    var defaultValue = getDefaultValue ? getDefaultValue() : undefined;\r\n    var _a = useReducer(reducer(), defaultState(defaultValue)), state = _a[0], dispatch = _a[1];\r\n    var reset = useCallback(function () {\r\n        var defaultValue = getDefaultValue ? getDefaultValue() : undefined;\r\n        dispatch({ type: 'reset', defaultValue: defaultValue });\r\n    }, [getDefaultValue]);\r\n    var setError = useCallback(function (error) {\r\n        dispatch({ type: 'error', error: error });\r\n    }, []);\r\n    var setValue = useCallback(function (value) {\r\n        dispatch({ type: 'value', value: value });\r\n    }, []);\r\n    return useMemo(function () { return ({\r\n        error: state.error,\r\n        loading: state.loading,\r\n        reset: reset,\r\n        setError: setError,\r\n        setValue: setValue,\r\n        value: state.value,\r\n    }); }, [state.error, state.loading, reset, setError, setValue, state.value]);\r\n});\n\nvar useAuthState = (function (auth, options) {\r\n    var _a = useLoadingValue(function () { return auth.currentUser; }), error = _a.error, loading = _a.loading, setError = _a.setError, setValue = _a.setValue, value = _a.value;\r\n    useEffect(function () {\r\n        var listener = onAuthStateChanged(auth, function (user) { return __awaiter(void 0, void 0, void 0, function () {\r\n            var e_1;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        if (!(options === null || options === void 0 ? void 0 : options.onUserChanged)) return [3 /*break*/, 4];\r\n                        _a.label = 1;\r\n                    case 1:\r\n                        _a.trys.push([1, 3, , 4]);\r\n                        return [4 /*yield*/, options.onUserChanged(user)];\r\n                    case 2:\r\n                        _a.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 3:\r\n                        e_1 = _a.sent();\r\n                        setError(e_1);\r\n                        return [3 /*break*/, 4];\r\n                    case 4:\r\n                        setValue(user);\r\n                        return [2 /*return*/];\r\n                }\r\n            });\r\n        }); }, setError);\r\n        return function () {\r\n            listener();\r\n        };\r\n    }, [auth]);\r\n    return [value, loading, error];\r\n});\n\nvar useCreateUserWithEmailAndPassword = (function (auth, options) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(), registeredUser = _b[0], setRegisteredUser = _b[1];\r\n    var _c = useState(false), loading = _c[0], setLoading = _c[1];\r\n    var createUserWithEmailAndPassword$1 = useCallback(function (email, password) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var user, error_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 5, 6, 7]);\r\n                    return [4 /*yield*/, createUserWithEmailAndPassword(auth, email, password)];\r\n                case 2:\r\n                    user = _a.sent();\r\n                    if (!(options && options.sendEmailVerification && user.user)) return [3 /*break*/, 4];\r\n                    return [4 /*yield*/, sendEmailVerification(user.user, options.emailVerificationOptions)];\r\n                case 3:\r\n                    _a.sent();\r\n                    _a.label = 4;\r\n                case 4:\r\n                    setRegisteredUser(user);\r\n                    return [2 /*return*/, user];\r\n                case 5:\r\n                    error_1 = _a.sent();\r\n                    setError(error_1);\r\n                    return [3 /*break*/, 7];\r\n                case 6:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth, options]);\r\n    return [createUserWithEmailAndPassword$1, registeredUser, loading, error];\r\n});\n\nvar useDeleteUser = (function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var deleteUser = useCallback(function () { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 5, 6, 7]);\r\n                    if (!auth.currentUser) return [3 /*break*/, 3];\r\n                    return [4 /*yield*/, auth.currentUser.delete()];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3: throw new Error('No user is logged in');\r\n                case 4: return [3 /*break*/, 7];\r\n                case 5:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [2 /*return*/, false];\r\n                case 6:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [deleteUser, loading, error];\r\n});\n\nvar useSendEmailVerification = (function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var sendEmailVerification$1 = useCallback(function () { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 5, 6, 7]);\r\n                    if (!auth.currentUser) return [3 /*break*/, 3];\r\n                    return [4 /*yield*/, sendEmailVerification(auth.currentUser)];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3: throw new Error('No user is logged in');\r\n                case 4: return [3 /*break*/, 7];\r\n                case 5:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [2 /*return*/, false];\r\n                case 6:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [sendEmailVerification$1, loading, error];\r\n});\n\nvar useSendPasswordResetEmail = (function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var sendPasswordResetEmail$1 = useCallback(function (email, actionCodeSettings) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 3, 4, 5]);\r\n                    return [4 /*yield*/, sendPasswordResetEmail(auth, email, actionCodeSettings)];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [2 /*return*/, false];\r\n                case 4:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [sendPasswordResetEmail$1, loading, error];\r\n});\n\nvar useSendSignInLinkToEmail = (function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var sendSignInLinkToEmail$1 = useCallback(function (email, actionCodeSettings) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 3, 4, 5]);\r\n                    return [4 /*yield*/, sendSignInLinkToEmail(auth, email, actionCodeSettings)];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [2 /*return*/, false];\r\n                case 4:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [sendSignInLinkToEmail$1, loading, error];\r\n});\n\nvar useSignInWithEmailAndPassword = (function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(), loggedInUser = _b[0], setLoggedInUser = _b[1];\r\n    var _c = useState(false), loading = _c[0], setLoading = _c[1];\r\n    var signInWithEmailAndPassword$1 = useCallback(function (email, password) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var user, err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 3, 4, 5]);\r\n                    return [4 /*yield*/, signInWithEmailAndPassword(auth, email, password)];\r\n                case 2:\r\n                    user = _a.sent();\r\n                    setLoggedInUser(user);\r\n                    return [2 /*return*/, user];\r\n                case 3:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [3 /*break*/, 5];\r\n                case 4:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [signInWithEmailAndPassword$1, loggedInUser, loading, error];\r\n});\n\nvar useSignInWithEmailLink = (function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(), loggedInUser = _b[0], setLoggedInUser = _b[1];\r\n    var _c = useState(false), loading = _c[0], setLoading = _c[1];\r\n    var signInWithEmailLink$1 = useCallback(function (email, emailLink) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var user, err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 3, 4, 5]);\r\n                    return [4 /*yield*/, signInWithEmailLink(auth, email, emailLink)];\r\n                case 2:\r\n                    user = _a.sent();\r\n                    setLoggedInUser(user);\r\n                    return [2 /*return*/, user];\r\n                case 3:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [3 /*break*/, 5];\r\n                case 4:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [signInWithEmailLink$1, loggedInUser, loading, error];\r\n});\n\nvar useSignInWithApple = function (auth) {\r\n    return useSignInWithOAuth(auth, 'apple.com');\r\n};\r\nvar useSignInWithFacebook = function (auth) {\r\n    var createFacebookAuthProvider = useCallback(function (scopes, customOAuthParameters) {\r\n        var provider = new FacebookAuthProvider();\r\n        if (scopes) {\r\n            scopes.forEach(function (scope) { return provider.addScope(scope); });\r\n        }\r\n        if (customOAuthParameters) {\r\n            provider.setCustomParameters(customOAuthParameters);\r\n        }\r\n        return provider;\r\n    }, []);\r\n    return useSignInWithPopup(auth, createFacebookAuthProvider);\r\n};\r\nvar useSignInWithGithub = function (auth) {\r\n    var createGithubAuthProvider = useCallback(function (scopes, customOAuthParameters) {\r\n        var provider = new GithubAuthProvider();\r\n        if (scopes) {\r\n            scopes.forEach(function (scope) { return provider.addScope(scope); });\r\n        }\r\n        if (customOAuthParameters) {\r\n            provider.setCustomParameters(customOAuthParameters);\r\n        }\r\n        return provider;\r\n    }, []);\r\n    return useSignInWithPopup(auth, createGithubAuthProvider);\r\n};\r\nvar useSignInWithGoogle = function (auth) {\r\n    var createGoogleAuthProvider = useCallback(function (scopes, customOAuthParameters) {\r\n        var provider = new GoogleAuthProvider();\r\n        if (scopes) {\r\n            scopes.forEach(function (scope) { return provider.addScope(scope); });\r\n        }\r\n        if (customOAuthParameters) {\r\n            provider.setCustomParameters(customOAuthParameters);\r\n        }\r\n        return provider;\r\n    }, []);\r\n    return useSignInWithPopup(auth, createGoogleAuthProvider);\r\n};\r\nvar useSignInWithMicrosoft = function (auth) {\r\n    return useSignInWithOAuth(auth, 'microsoft.com');\r\n};\r\nvar useSignInWithTwitter = function (auth) {\r\n    var createTwitterAuthProvider = useCallback(function (scopes, customOAuthParameters) {\r\n        var provider = new TwitterAuthProvider();\r\n        if (scopes) {\r\n            scopes.forEach(function (scope) { return provider.addScope(scope); });\r\n        }\r\n        if (customOAuthParameters) {\r\n            provider.setCustomParameters(customOAuthParameters);\r\n        }\r\n        return provider;\r\n    }, []);\r\n    return useSignInWithPopup(auth, createTwitterAuthProvider);\r\n};\r\nvar useSignInWithYahoo = function (auth) {\r\n    return useSignInWithOAuth(auth, 'yahoo.com');\r\n};\r\nvar useSignInWithOAuth = function (auth, providerId) {\r\n    var createOAuthProvider = useCallback(function (scopes, customOAuthParameters) {\r\n        var provider = new OAuthProvider(providerId);\r\n        if (scopes) {\r\n            scopes.forEach(function (scope) { return provider.addScope(scope); });\r\n        }\r\n        if (customOAuthParameters) {\r\n            provider.setCustomParameters(customOAuthParameters);\r\n        }\r\n        return provider;\r\n    }, [providerId]);\r\n    return useSignInWithPopup(auth, createOAuthProvider);\r\n};\r\nvar useSignInWithPopup = function (auth, createProvider) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(), loggedInUser = _b[0], setLoggedInUser = _b[1];\r\n    var _c = useState(false), loading = _c[0], setLoading = _c[1];\r\n    var doSignInWithPopup = useCallback(function (scopes, customOAuthParameters) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var provider, user, err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 3, 4, 5]);\r\n                    provider = createProvider(scopes, customOAuthParameters);\r\n                    return [4 /*yield*/, signInWithPopup(auth, provider)];\r\n                case 2:\r\n                    user = _a.sent();\r\n                    setLoggedInUser(user);\r\n                    return [2 /*return*/, user];\r\n                case 3:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [3 /*break*/, 5];\r\n                case 4:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth, createProvider]);\r\n    return [doSignInWithPopup, loggedInUser, loading, error];\r\n};\n\nvar useSignOut = (function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var signOut = useCallback(function () { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 3, 4, 5]);\r\n                    return [4 /*yield*/, auth.signOut()];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [2 /*return*/, false];\r\n                case 4:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [signOut, loading, error];\r\n});\n\nvar useUpdateEmail = function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var updateEmail$1 = useCallback(function (email) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_1;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 5, 6, 7]);\r\n                    if (!auth.currentUser) return [3 /*break*/, 3];\r\n                    return [4 /*yield*/, updateEmail(auth.currentUser, email)];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3: throw new Error('No user is logged in');\r\n                case 4: return [3 /*break*/, 7];\r\n                case 5:\r\n                    err_1 = _a.sent();\r\n                    setError(err_1);\r\n                    return [2 /*return*/, false];\r\n                case 6:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [updateEmail$1, loading, error];\r\n};\r\nvar useUpdatePassword = function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var updatePassword$1 = useCallback(function (password) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_2;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 5, 6, 7]);\r\n                    if (!auth.currentUser) return [3 /*break*/, 3];\r\n                    return [4 /*yield*/, updatePassword(auth.currentUser, password)];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3: throw new Error('No user is logged in');\r\n                case 4: return [3 /*break*/, 7];\r\n                case 5:\r\n                    err_2 = _a.sent();\r\n                    setError(err_2);\r\n                    return [2 /*return*/, false];\r\n                case 6:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [updatePassword$1, loading, error];\r\n};\r\nvar useUpdateProfile = function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var updateProfile$1 = useCallback(function (profile) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_3;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 5, 6, 7]);\r\n                    if (!auth.currentUser) return [3 /*break*/, 3];\r\n                    return [4 /*yield*/, updateProfile(auth.currentUser, profile)];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3: throw new Error('No user is logged in');\r\n                case 4: return [3 /*break*/, 7];\r\n                case 5:\r\n                    err_3 = _a.sent();\r\n                    setError(err_3);\r\n                    return [2 /*return*/, false];\r\n                case 6:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [updateProfile$1, loading, error];\r\n};\r\nvar useVerifyBeforeUpdateEmail = function (auth) {\r\n    var _a = useState(), error = _a[0], setError = _a[1];\r\n    var _b = useState(false), loading = _b[0], setLoading = _b[1];\r\n    var verifyBeforeUpdateEmail$1 = useCallback(function (email, actionCodeSettings) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var err_4;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    setLoading(true);\r\n                    setError(undefined);\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 5, 6, 7]);\r\n                    if (!auth.currentUser) return [3 /*break*/, 3];\r\n                    return [4 /*yield*/, verifyBeforeUpdateEmail(auth.currentUser, email, actionCodeSettings)];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, true];\r\n                case 3: throw new Error('No user is logged in');\r\n                case 4: return [3 /*break*/, 7];\r\n                case 5:\r\n                    err_4 = _a.sent();\r\n                    setError(err_4);\r\n                    return [2 /*return*/, false];\r\n                case 6:\r\n                    setLoading(false);\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [auth]);\r\n    return [verifyBeforeUpdateEmail$1, loading, error];\r\n};\n\nvar useIdToken = (function (auth, options) {\r\n    var _a = useLoadingValue(function () { return auth.currentUser; }), error = _a.error, loading = _a.loading, setError = _a.setError, setValue = _a.setValue, value = _a.value;\r\n    useEffect(function () {\r\n        var listener = onIdTokenChanged(auth, function (user) { return __awaiter(void 0, void 0, void 0, function () {\r\n            var e_1;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        if (!(options === null || options === void 0 ? void 0 : options.onUserChanged)) return [3 /*break*/, 4];\r\n                        _a.label = 1;\r\n                    case 1:\r\n                        _a.trys.push([1, 3, , 4]);\r\n                        return [4 /*yield*/, options.onUserChanged(user)];\r\n                    case 2:\r\n                        _a.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 3:\r\n                        e_1 = _a.sent();\r\n                        setError(e_1);\r\n                        return [3 /*break*/, 4];\r\n                    case 4:\r\n                        setValue(user);\r\n                        return [2 /*return*/];\r\n                }\r\n            });\r\n        }); }, setError);\r\n        return function () {\r\n            listener();\r\n        };\r\n    }, [auth]);\r\n    return [value, loading, error];\r\n});\n\nexport { useAuthState, useCreateUserWithEmailAndPassword, useDeleteUser, useIdToken, useSendEmailVerification, useSendPasswordResetEmail, useSendSignInLinkToEmail, useSignInWithApple, useSignInWithEmailAndPassword, useSignInWithEmailLink, useSignInWithFacebook, useSignInWithGithub, useSignInWithGoogle, useSignInWithMicrosoft, useSignInWithTwitter, useSignInWithYahoo, useSignOut, useUpdateEmail, useUpdatePassword, useUpdateProfile, useVerifyBeforeUpdateEmail };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,mBAAsE;AAiBtE,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AAClD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEA,SAAS,YAAY,SAAS,MAAM;AAChC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,EAAG,KAAI;AACV,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAK,EAAE,IAAI,IAAI;AAAG,YAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,YAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B,SAAS,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;AAAA,IAAG,UAAE;AAAU,UAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAiBA,IAAI,WAAW,WAAW;AACtB,aAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEA,IAAI,eAAe,SAAU,cAAc;AACvC,SAAO;AAAA,IACH,SAAS,iBAAiB,UAAa,iBAAiB;AAAA,IACxD,OAAO;AAAA,EACX;AACJ;AACA,IAAI,UAAU,WAAY;AAAE,SAAO,SAAU,OAAO,QAAQ;AACxD,YAAQ,OAAO,MAAM;AAAA,MACjB,KAAK;AACD,eAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,OAAO,OAAO,OAAO,SAAS,OAAO,OAAO,OAAU,CAAC;AAAA,MAClG,KAAK;AACD,eAAO,aAAa,OAAO,YAAY;AAAA,MAC3C,KAAK;AACD,eAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,OAAO,QAAW,SAAS,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,MAClG;AACI,eAAO;AAAA,IACf;AAAA,EACJ;AAAG;AACH,IAAI,kBAAmB,SAAU,iBAAiB;AAC9C,MAAI,eAAe,kBAAkB,gBAAgB,IAAI;AACzD,MAAI,SAAK,yBAAW,QAAQ,GAAG,aAAa,YAAY,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC1F,MAAI,YAAQ,0BAAY,WAAY;AAChC,QAAIC,gBAAe,kBAAkB,gBAAgB,IAAI;AACzD,aAAS,EAAE,MAAM,SAAS,cAAcA,cAAa,CAAC;AAAA,EAC1D,GAAG,CAAC,eAAe,CAAC;AACpB,MAAI,eAAW,0BAAY,SAAU,OAAO;AACxC,aAAS,EAAE,MAAM,SAAS,MAAa,CAAC;AAAA,EAC5C,GAAG,CAAC,CAAC;AACL,MAAI,eAAW,0BAAY,SAAU,OAAO;AACxC,aAAS,EAAE,MAAM,SAAS,MAAa,CAAC;AAAA,EAC5C,GAAG,CAAC,CAAC;AACL,aAAO,sBAAQ,WAAY;AAAE,WAAQ;AAAA,MACjC,OAAO,MAAM;AAAA,MACb,SAAS,MAAM;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,MAAM;AAAA,IACjB;AAAA,EAAI,GAAG,CAAC,MAAM,OAAO,MAAM,SAAS,OAAO,UAAU,UAAU,MAAM,KAAK,CAAC;AAC/E;AAEA,IAAI,eAAgB,SAAU,MAAM,SAAS;AACzC,MAAI,KAAK,gBAAgB,WAAY;AAAE,WAAO,KAAK;AAAA,EAAa,CAAC,GAAG,QAAQ,GAAG,OAAO,UAAU,GAAG,SAAS,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvK,8BAAU,WAAY;AAClB,QAAI,WAAW,mBAAmB,MAAM,SAAU,MAAM;AAAE,aAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AAC3G,YAAI;AACJ,eAAO,YAAY,MAAM,SAAUC,KAAI;AACnC,kBAAQA,IAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAgB,QAAO,CAAC,GAAa,CAAC;AACtG,cAAAA,IAAG,QAAQ;AAAA,YACf,KAAK;AACD,cAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,qBAAO,CAAC,GAAa,QAAQ,cAAc,IAAI,CAAC;AAAA,YACpD,KAAK;AACD,cAAAA,IAAG,KAAK;AACR,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,oBAAMA,IAAG,KAAK;AACd,uBAAS,GAAG;AACZ,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,uBAAS,IAAI;AACb,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IAAG,GAAG,QAAQ;AACf,WAAO,WAAY;AACf,eAAS;AAAA,IACb;AAAA,EACJ,GAAG,CAAC,IAAI,CAAC;AACT,SAAO,CAAC,OAAO,SAAS,KAAK;AACjC;AAEA,IAAI,oCAAqC,SAAU,MAAM,SAAS;AAC9D,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,GAAG,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;AACrE,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,uCAAmC,0BAAY,SAAU,OAAO,UAAU;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACjI,UAAI,MAAM;AACV,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,mBAAO,CAAC,GAAa,+BAA+B,MAAM,OAAO,QAAQ,CAAC;AAAA,UAC9E,KAAK;AACD,mBAAOA,IAAG,KAAK;AACf,gBAAI,EAAE,WAAW,QAAQ,yBAAyB,KAAK,MAAO,QAAO,CAAC,GAAa,CAAC;AACpF,mBAAO,CAAC,GAAa,sBAAsB,KAAK,MAAM,QAAQ,wBAAwB,CAAC;AAAA,UAC3F,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,8BAAkB,IAAI;AACtB,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AACD,sBAAUA,IAAG,KAAK;AAClB,qBAAS,OAAO;AAChB,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC1B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,MAAM,OAAO,CAAC;AACtB,SAAO,CAAC,kCAAkC,gBAAgB,SAAS,KAAK;AAC5E;AAEA,IAAI,gBAAiB,SAAU,MAAM;AACjC,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,iBAAa,0BAAY,WAAY;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AAC5F,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,gBAAI,CAAC,KAAK,YAAa,QAAO,CAAC,GAAa,CAAC;AAC7C,mBAAO,CAAC,GAAa,KAAK,YAAY,OAAO,CAAC;AAAA,UAClD,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AAAG,kBAAM,IAAI,MAAM,sBAAsB;AAAA,UAC9C,KAAK;AAAG,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,YAAY,SAAS,KAAK;AACtC;AAEA,IAAI,2BAA4B,SAAU,MAAM;AAC5C,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,8BAA0B,0BAAY,WAAY;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACzG,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,gBAAI,CAAC,KAAK,YAAa,QAAO,CAAC,GAAa,CAAC;AAC7C,mBAAO,CAAC,GAAa,sBAAsB,KAAK,WAAW,CAAC;AAAA,UAChE,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AAAG,kBAAM,IAAI,MAAM,sBAAsB;AAAA,UAC9C,KAAK;AAAG,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,yBAAyB,SAAS,KAAK;AACnD;AAEA,IAAI,4BAA6B,SAAU,MAAM;AAC7C,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,+BAA2B,0BAAY,SAAU,OAAO,oBAAoB;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACnI,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,mBAAO,CAAC,GAAa,uBAAuB,MAAM,OAAO,kBAAkB,CAAC;AAAA,UAChF,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,0BAA0B,SAAS,KAAK;AACpD;AAEA,IAAI,2BAA4B,SAAU,MAAM;AAC5C,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,8BAA0B,0BAAY,SAAU,OAAO,oBAAoB;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AAClI,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,mBAAO,CAAC,GAAa,sBAAsB,MAAM,OAAO,kBAAkB,CAAC;AAAA,UAC/E,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,yBAAyB,SAAS,KAAK;AACnD;AAEA,IAAI,gCAAiC,SAAU,MAAM;AACjD,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,GAAG,eAAe,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AACjE,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,mCAA+B,0BAAY,SAAU,OAAO,UAAU;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AAC7H,UAAI,MAAM;AACV,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,mBAAO,CAAC,GAAa,2BAA2B,MAAM,OAAO,QAAQ,CAAC;AAAA,UAC1E,KAAK;AACD,mBAAOA,IAAG,KAAK;AACf,4BAAgB,IAAI;AACpB,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC1B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,8BAA8B,cAAc,SAAS,KAAK;AACtE;AAEA,IAAI,yBAA0B,SAAU,MAAM;AAC1C,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,GAAG,eAAe,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AACjE,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,4BAAwB,0BAAY,SAAU,OAAO,WAAW;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACvH,UAAI,MAAM;AACV,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,mBAAO,CAAC,GAAa,oBAAoB,MAAM,OAAO,SAAS,CAAC;AAAA,UACpE,KAAK;AACD,mBAAOA,IAAG,KAAK;AACf,4BAAgB,IAAI;AACpB,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC1B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,uBAAuB,cAAc,SAAS,KAAK;AAC/D;AAEA,IAAI,qBAAqB,SAAU,MAAM;AACrC,SAAO,mBAAmB,MAAM,WAAW;AAC/C;AACA,IAAI,wBAAwB,SAAU,MAAM;AACxC,MAAI,iCAA6B,0BAAY,SAAU,QAAQ,uBAAuB;AAClF,QAAI,WAAW,IAAI,qBAAqB;AACxC,QAAI,QAAQ;AACR,aAAO,QAAQ,SAAU,OAAO;AAAE,eAAO,SAAS,SAAS,KAAK;AAAA,MAAG,CAAC;AAAA,IACxE;AACA,QAAI,uBAAuB;AACvB,eAAS,oBAAoB,qBAAqB;AAAA,IACtD;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO,mBAAmB,MAAM,0BAA0B;AAC9D;AACA,IAAI,sBAAsB,SAAU,MAAM;AACtC,MAAI,+BAA2B,0BAAY,SAAU,QAAQ,uBAAuB;AAChF,QAAI,WAAW,IAAI,mBAAmB;AACtC,QAAI,QAAQ;AACR,aAAO,QAAQ,SAAU,OAAO;AAAE,eAAO,SAAS,SAAS,KAAK;AAAA,MAAG,CAAC;AAAA,IACxE;AACA,QAAI,uBAAuB;AACvB,eAAS,oBAAoB,qBAAqB;AAAA,IACtD;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO,mBAAmB,MAAM,wBAAwB;AAC5D;AACA,IAAI,sBAAsB,SAAU,MAAM;AACtC,MAAI,+BAA2B,0BAAY,SAAU,QAAQ,uBAAuB;AAChF,QAAI,WAAW,IAAI,mBAAmB;AACtC,QAAI,QAAQ;AACR,aAAO,QAAQ,SAAU,OAAO;AAAE,eAAO,SAAS,SAAS,KAAK;AAAA,MAAG,CAAC;AAAA,IACxE;AACA,QAAI,uBAAuB;AACvB,eAAS,oBAAoB,qBAAqB;AAAA,IACtD;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO,mBAAmB,MAAM,wBAAwB;AAC5D;AACA,IAAI,yBAAyB,SAAU,MAAM;AACzC,SAAO,mBAAmB,MAAM,eAAe;AACnD;AACA,IAAI,uBAAuB,SAAU,MAAM;AACvC,MAAI,gCAA4B,0BAAY,SAAU,QAAQ,uBAAuB;AACjF,QAAI,WAAW,IAAI,oBAAoB;AACvC,QAAI,QAAQ;AACR,aAAO,QAAQ,SAAU,OAAO;AAAE,eAAO,SAAS,SAAS,KAAK;AAAA,MAAG,CAAC;AAAA,IACxE;AACA,QAAI,uBAAuB;AACvB,eAAS,oBAAoB,qBAAqB;AAAA,IACtD;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO,mBAAmB,MAAM,yBAAyB;AAC7D;AACA,IAAI,qBAAqB,SAAU,MAAM;AACrC,SAAO,mBAAmB,MAAM,WAAW;AAC/C;AACA,IAAI,qBAAqB,SAAU,MAAM,YAAY;AACjD,MAAI,0BAAsB,0BAAY,SAAU,QAAQ,uBAAuB;AAC3E,QAAI,WAAW,IAAI,cAAc,UAAU;AAC3C,QAAI,QAAQ;AACR,aAAO,QAAQ,SAAU,OAAO;AAAE,eAAO,SAAS,SAAS,KAAK;AAAA,MAAG,CAAC;AAAA,IACxE;AACA,QAAI,uBAAuB;AACvB,eAAS,oBAAoB,qBAAqB;AAAA,IACtD;AACA,WAAO;AAAA,EACX,GAAG,CAAC,UAAU,CAAC;AACf,SAAO,mBAAmB,MAAM,mBAAmB;AACvD;AACA,IAAI,qBAAqB,SAAU,MAAM,gBAAgB;AACrD,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,GAAG,eAAe,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AACjE,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,wBAAoB,0BAAY,SAAU,QAAQ,uBAAuB;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AAChI,UAAI,UAAU,MAAM;AACpB,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,uBAAW,eAAe,QAAQ,qBAAqB;AACvD,mBAAO,CAAC,GAAa,gBAAgB,MAAM,QAAQ,CAAC;AAAA,UACxD,KAAK;AACD,mBAAOA,IAAG,KAAK;AACf,4BAAgB,IAAI;AACpB,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC1B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,MAAM,cAAc,CAAC;AAC7B,SAAO,CAAC,mBAAmB,cAAc,SAAS,KAAK;AAC3D;AAEA,IAAI,aAAc,SAAU,MAAM;AAC9B,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,cAAU,0BAAY,WAAY;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACzF,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,mBAAO,CAAC,GAAa,KAAK,QAAQ,CAAC;AAAA,UACvC,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,SAAS,SAAS,KAAK;AACnC;AAEA,IAAI,iBAAiB,SAAU,MAAM;AACjC,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,oBAAgB,0BAAY,SAAU,OAAO;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACpG,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,gBAAI,CAAC,KAAK,YAAa,QAAO,CAAC,GAAa,CAAC;AAC7C,mBAAO,CAAC,GAAa,YAAY,KAAK,aAAa,KAAK,CAAC;AAAA,UAC7D,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AAAG,kBAAM,IAAI,MAAM,sBAAsB;AAAA,UAC9C,KAAK;AAAG,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,eAAe,SAAS,KAAK;AACzC;AACA,IAAI,oBAAoB,SAAU,MAAM;AACpC,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,uBAAmB,0BAAY,SAAU,UAAU;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AAC1G,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,gBAAI,CAAC,KAAK,YAAa,QAAO,CAAC,GAAa,CAAC;AAC7C,mBAAO,CAAC,GAAa,eAAe,KAAK,aAAa,QAAQ,CAAC;AAAA,UACnE,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AAAG,kBAAM,IAAI,MAAM,sBAAsB;AAAA,UAC9C,KAAK;AAAG,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,kBAAkB,SAAS,KAAK;AAC5C;AACA,IAAI,mBAAmB,SAAU,MAAM;AACnC,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,sBAAkB,0BAAY,SAAU,SAAS;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACxG,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,gBAAI,CAAC,KAAK,YAAa,QAAO,CAAC,GAAa,CAAC;AAC7C,mBAAO,CAAC,GAAa,cAAc,KAAK,aAAa,OAAO,CAAC;AAAA,UACjE,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AAAG,kBAAM,IAAI,MAAM,sBAAsB;AAAA,UAC9C,KAAK;AAAG,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,iBAAiB,SAAS,KAAK;AAC3C;AACA,IAAI,6BAA6B,SAAU,MAAM;AAC7C,MAAI,SAAK,uBAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACnD,MAAI,SAAK,uBAAS,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC5D,MAAI,gCAA4B,0BAAY,SAAU,OAAO,oBAAoB;AAAE,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACpI,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AACD,uBAAW,IAAI;AACf,qBAAS,MAAS;AAClB,YAAAA,IAAG,QAAQ;AAAA,UACf,KAAK;AACD,YAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,gBAAI,CAAC,KAAK,YAAa,QAAO,CAAC,GAAa,CAAC;AAC7C,mBAAO,CAAC,GAAa,wBAAwB,KAAK,aAAa,OAAO,kBAAkB,CAAC;AAAA,UAC7F,KAAK;AACD,YAAAA,IAAG,KAAK;AACR,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B,KAAK;AAAG,kBAAM,IAAI,MAAM,sBAAsB;AAAA,UAC9C,KAAK;AAAG,mBAAO,CAAC,GAAa,CAAC;AAAA,UAC9B,KAAK;AACD,oBAAQA,IAAG,KAAK;AAChB,qBAAS,KAAK;AACd,mBAAO,CAAC,GAAc,KAAK;AAAA,UAC/B,KAAK;AACD,uBAAW,KAAK;AAChB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC5B,KAAK;AAAG,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,IAAI,CAAC;AACb,SAAO,CAAC,2BAA2B,SAAS,KAAK;AACrD;AAEA,IAAI,aAAc,SAAU,MAAM,SAAS;AACvC,MAAI,KAAK,gBAAgB,WAAY;AAAE,WAAO,KAAK;AAAA,EAAa,CAAC,GAAG,QAAQ,GAAG,OAAO,UAAU,GAAG,SAAS,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvK,8BAAU,WAAY;AAClB,QAAI,WAAW,iBAAiB,MAAM,SAAU,MAAM;AAAE,aAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACzG,YAAI;AACJ,eAAO,YAAY,MAAM,SAAUA,KAAI;AACnC,kBAAQA,IAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAgB,QAAO,CAAC,GAAa,CAAC;AACtG,cAAAA,IAAG,QAAQ;AAAA,YACf,KAAK;AACD,cAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,qBAAO,CAAC,GAAa,QAAQ,cAAc,IAAI,CAAC;AAAA,YACpD,KAAK;AACD,cAAAA,IAAG,KAAK;AACR,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,oBAAMA,IAAG,KAAK;AACd,uBAAS,GAAG;AACZ,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,uBAAS,IAAI;AACb,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IAAG,GAAG,QAAQ;AACf,WAAO,WAAY;AACf,eAAS;AAAA,IACb;AAAA,EACJ,GAAG,CAAC,IAAI,CAAC;AACT,SAAO,CAAC,OAAO,SAAS,KAAK;AACjC;", "names": ["__assign", "defaultValue", "_a"]}